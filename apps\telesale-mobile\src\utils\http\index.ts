import Axios, {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  Method
} from "axios";
import { closeToast, showDialog, showToast } from "vant";
import { createAxios } from "@telesale/server";

const defaultConfig: AxiosRequestConfig = {
  baseURL: import.meta.env.VITE_API_URL,
  timeout: import.meta.env.MODE === "master" ? 30000 : 60000
};

interface IAxiosConfig extends AxiosRequestConfig {
  /**
   * @description: 是否显示错误提示：默认显示
   */
  showErrorToast?: boolean;
  /**
   * @description: 是否需要返回的数据用data包裹 { data: ReturnValue }
   */
  isData?: boolean;
}

class RequestHttp {
  constructor() {
    this.httpInterceptorsRequest();
    this.httpInterceptorsResponse();
  }

  // 保存当前Axios实例对象
  private static axiosInstance: AxiosInstance = Axios.create(defaultConfig);

  // 请求拦截
  private httpInterceptorsRequest(): void {
    RequestHttp.axiosInstance.interceptors.request.use(
      (config: IAxiosConfig) => {
        const $config = config;
        const ShadowAuthorization = localStorage.getItem("whcrmAuthorization");

        $config.headers!.common["x-enabled-behavior"] = true;
        ShadowAuthorization &&
          ($config.headers!.common["ShadowAuthorization"] =
            ShadowAuthorization);
        return $config;
      },
      error => {
        return Promise.reject(error);
      }
    );
  }

  // 响应拦截
  private httpInterceptorsResponse(): void {
    const instance = RequestHttp.axiosInstance;
    instance.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error: AxiosError) => {
        const { showErrorToast = true } = error.config as IAxiosConfig;
        if (error.response?.data) {
          // 403和401排除上面两种统一提示网络异常
          if (error.response.status === 401) {
            closeToast();
            showDialog({
              title: "提示",
              message: "登录已过期"
            }).then(() => {
              localStorage.removeItem("whcrmAuthorization");
              window.history.replaceState(
                null,
                "",
                `${location.origin}/WH_CRM_v2/telesale-mobile/`
              );
              window.history.go(0);
            });
          } else if (error.response.status === 403) {
            closeToast();
            showDialog({
              title: "提示",
              message: "您暂未在本系统注册坐席，请联系您的主管！"
            }).then(() => {
              window.tt.closeWindow();
            });
          } else {
            const data: any = error.response.data;
            if (error.config?.responseType === "arraybuffer") {
              // 处理为arraybuffer的情况
              const enc = new TextDecoder("utf-8");
              const arr = new Uint8Array(data as ArrayBuffer);
              const text = enc.decode(arr);
              showErrorToast && showToast(text);
            } else {
              showErrorToast && showToast(data.message || data.msg || data);
            }
          }
        } else {
          showErrorToast && showToast("网络异常或调用超时");
        }
        // 所有的响应异常 区分来源为取消请求/非取消请求
        return Promise.reject(error);
      }
    );
  }

  // 通用请求工具函数
  public request<T>(
    method: Method,
    url: string,
    param?: IAxiosConfig,
    axiosConfig?: IAxiosConfig
  ): Promise<T> {
    const config = {
      method,
      url,
      ...param,
      ...axiosConfig
    } as IAxiosConfig;

    // 单独处理自定义请求/响应回掉
    return new Promise((resolve, reject) => {
      RequestHttp.axiosInstance
        .request(config)
        .then(response => {
          resolve(
            config.isData === true
              ? (response as AxiosResponse<T>)
              : response.data
          );
        })
        .catch(error => {
          reject(error);
        });
    });
  }
}

// 初始化公用API的axios

export const http = new RequestHttp();

createAxios(http);
