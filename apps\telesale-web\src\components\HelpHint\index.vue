<!--
 * @Date         : 2024-06-20 17:26:05
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script setup lang="ts">
import { computed, ref } from "vue";
import tipsObj from "/@/utils/data/tipsObj";
import FilterDropdown from "../ReTable/components/FilterDropdown.vue";
import FilterCascader from "../ReTable/components/FilterCascader.vue";

interface Props {
  item: any;
}

const props = defineProps<Props>();

const emits = defineEmits(["filterHeadData"]);

const filterDropdownRef = ref<InstanceType<typeof FilterDropdown>>();

const filterValue = ref();

const filterHandle = (val: any, isFilter: boolean = true) => {
  filterValue.value = val;
  if (isFilter) {
    emits("filterHeadData", { [props.item.field]: val });
  }
};

const getTextClass = computed(() => {
  if (
    props.item.filterOptions?.isMultiple ||
    props.item.filterCascaderOptions
  ) {
    return filterValue.value?.length > 0 ? "c-#409EFF" : "";
  } else {
    return filterValue.value || filterValue.value === false ? "c-#409EFF" : "";
  }
});

const resetFilterData = () => {
  filterValue.value = props.item.filterOptions?.defaultFilterValue;
  filterDropdownRef.value?.resetData();
};

const clearFilterData = () => {
  filterValue.value = undefined;
  filterDropdownRef.value?.clearData();
};

defineExpose({
  resetFilterData,
  clearFilterData
});
</script>

<template>
  <span :class="getTextClass">
    {{ props.item.desc }}
    <span v-if="props.item.headerTip">
      <!--          有无tip-->
      <el-popover placement="top" width="250" trigger="hover">
        <span v-html="tipsObj[props.item.field]" />
        <template #reference>
          <IconifyIconOffline
            icon="question"
            style="cursor: pointer; color: #ccc; vertical-align: text-top"
          />
        </template>
      </el-popover>
    </span>
    <div v-if="props.item.filterOptions" class="inline-block align-mid">
      <FilterDropdown
        :rowKey="props.item.field + 'filterDropdown'"
        ref="filterDropdownRef"
        v-bind="props.item.filterOptions"
        @filter="filterHandle"
      />
    </div>
    <div v-if="props.item.filterCascaderOptions" class="inline-block align-mid">
      <FilterCascader
        ref="filterDropdownRef"
        v-bind="props.item.filterCascaderOptions"
        @filter="filterHandle"
      />
    </div>
  </span>
</template>
