import { http } from "../utils/http";
import baseURL from "./url";

//截图列表
export const getListReview = params => {
  return http.request<{
    total: number;
    list: any[];
  }>("get", `${baseURL.api}/web/share_appeal`, { params });
};

//截图审核详情
export const detailReview = params => {
  return http.request("get", `${baseURL.api}/web/share_appeal/detail`, {
    params
  });
};

//截图审核
export const reviewPicture = data => {
  return http.request("put", `${baseURL.api}/web/share_appeal`, { data });
};

//获取下一条
export const getNext = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-marketing/share_appeal/next`,
    {
      params
    }
  );
};

//商品列表
export const getListGoods = params => {
  return http.request("get", `${baseURL.api}/web/goods`, { params });
};

//切换商品上下架状态
export const statusGoods = id => {
  return http.request("put", `${baseURL.api}/web/goods/switch/${id}`);
};

//删除商品
export const delGoods = id => {
  return http.request("delete", `${baseURL.api}/web/goods/${id}`);
};

//根据ID获取商品信息
export const detailGoods = id => {
  return http.request("get", `${baseURL.api}/web/goods/${id}`);
};

//根据ID获取中台商品信息
export const detailGoodsVirtual = id => {
  return http.request("get", `${baseURL.api}/web/virtual/goods/${id}`);
};

//添加商品
export const addGoods = data => {
  return http.request("post", `${baseURL.api}/web/goods`, { data });
};

//修改商品
export const editGoods = data => {
  return http.request("put", `${baseURL.api}/web/goods`, { data });
};

//商品标签列表
export const goodsTags = () => {
  return http.request("get", `${baseURL.api}/web/tags`);
};

//积分明细
export const detailIntegral = params => {
  return http.request("get", `${baseURL.api}/web/point/detail`, { params });
};

//订单列表
export const getListOrder = params => {
  return http.request("get", `${baseURL.api}/web/marketing/order`, { params });
};

//订单详情
export const detailOrder = id => {
  return http.request("get", `${baseURL.api}/web/marketing/order/${id}`);
};

//重新授权
export const reauthorize = id => {
  return http.request(
    "put",
    `${baseURL.api}/web/marketing/order/reauthorize/${id}`
  );
};

//更新物流
export const logistics = data => {
  return http.request(
    "put",
    `${baseURL.api}/web/marketing/orderShip/${data.id}`,
    {
      data
    }
  );
};

//导出订单
export const orderExport = params => {
  return http.request(
    "get",
    `${baseURL.api}/web/marketing/orderExport`,
    {
      params
    },
    {
      responseType: "blob"
    }
  );
};

// 后台 - 解析批量发货订单的Excel文件
export const orderUpload = data => {
  return http.request(
    "post",
    `${baseURL.api}/web/marketing/orderParse`,
    {
      data
    },
    {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

//后台 - 根据导入的文件批量发货
export const importOrder = data => {
  return http.request("put", `${baseURL.api}/web/marketing/order/batch/ship`, {
    data
  });
};

//新版根据任务Id，查询后台异步任务
export const taskOrder = params => {
  return http.request(
    "get",
    `${baseURL.api}/web/marketing/batch/shipDetail/${params.id}`,
    {
      params
    }
  );
};

// 获取运费模板列表
export const getFreightTypeApi = freightType => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-marketing/freightTemplate/${freightType}`
  );
};

// 获取运费模板详情
export const getFreightInfoApi = id => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-marketing/freightTemplate/take/${id}`
  );
};

// 创建运费模板列表
export const addFreightTypeApi = (freightType, data) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-marketing/freightTemplate/${freightType}`,
    { data }
  );
};

// 更新运费模板列表
export const updateFreightTypeApi = data => {
  return http.request("put", `${baseURL.api}/wuhan-marketing/freightTemplate`, {
    data
  });
};

// 删除运费模板列表
export const delFreightTypeApi = id => {
  return http.request(
    "delete",
    `${baseURL.api}/wuhan-marketing/freightTemplate/${id}`
  );
};

// 批量保存禁止发货模板
export const batchDisableApi = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-marketing/freightTemplate/disable/batch`,
    { data }
  );
};
