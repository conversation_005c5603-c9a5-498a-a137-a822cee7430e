<script lang="ts" setup>
import { getFileApi, uploadFileApi } from "@/api/common";
import { useVModel } from "@vueuse/core";
import { isArray } from "lodash-es";
import { closeToast, showLoadingToast, showToast } from "vant";

const props = defineProps<{
  value: any[];
}>();

const emits = defineEmits(["update:value"]);

const fileList = useVModel(props, "value", emits);
const count = ref<number>(0);

const afterRead = file => {
  // showLoadingToast({});
  if (isArray(file)) {
    file.forEach(item => {
      const form = new FormData();
      form.append("file", item.file);
      item.status = "uploading";
      uploadFile(form, item);
    });
  } else {
    const form = new FormData();
    form.append("file", file.file);
    file.status = "uploading";
    uploadFile(form, file);
  }
};

const uploadFile = (form: FormData, file) => {
  count.value++;
  uploadFileApi(form)
    .then(res => {
      file.status = "done";
      file.link = res;
      // return getFileAddress(res, file);
    })
    .catch(() => {
      file.status = "failed";
      file.message = "上传失败";
    })
    .finally(() => {
      count.value--;
      if (count.value === 0) {
        // closeToast();
      }
    });
};

const getFileAddress = (path: string, file) => {
  getFileApi({ object: path })
    .then(res => {
      file.url = res;
    })
    .finally(() => {
      count.value--;
      if (count.value === 0) {
        closeToast();
      }
    });
};
</script>

<template>
  <div>
    <van-uploader v-model="fileList" :afterRead="afterRead" v-bind="$attrs" />
  </div>
</template>

<style lang="scss" scoped></style>
