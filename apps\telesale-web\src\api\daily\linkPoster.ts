/*
 * @Date         : 2024-07-24 18:40:02
 * @Description  : 课程海报相关api
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";
import baseURL from "../url";

//获取转介绍活动列表
export const getClassPosterApi = params => {
  return http.request(
    "get",
    `${baseURL.api}/sync-order/course/listCoursePoster`,
    {
      params
    }
  );
};

//添加转介绍活动
export const addClassPosterApi = data => {
  return http.request(
    "post",
    `${baseURL.api}/sync-order/course/createCoursePoster`,
    {
      data
    }
  );
};

//更新转介绍活动
export const editClassPosterApi = data => {
  return http.request(
    "put",
    `${baseURL.api}/sync-order/course/updateCoursePoster`,
    {
      data
    }
  );
};

//转介绍活动详情
export const getClassPosterInfoApi = params => {
  return http.request(
    "get",
    `${baseURL.api}/sync-order/course/getCoursePoster`,
    {
      params
    }
  );
};

//上下架
export const updateClassPosterStatus = data => {
  return http.request(
    "post",
    `${baseURL.api}/sync-order/course/releaseCoursePoster`,
    {
      data
    }
  );
};
