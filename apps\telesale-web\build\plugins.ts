import vue from "@vitejs/plugin-vue";
import { viteBuildInfo } from "./info";
import svgLoader from "vite-svg-loader";
import vueJsx from "@vitejs/plugin-vue-jsx";
import { visualizer } from "rollup-plugin-visualizer";
import themePreprocessorPlugin from "@pureadmin/theme";
import { genScssMultipleScopeVars } from "/@/layout/theme";
import VueSetupExtend from "vite-plugin-vue-setup-extend";
import qiankun from "vite-plugin-qiankun";
import {
  createStyleImportPlugin,
  VxeTableResolve
} from "vite-plugin-style-import";
import UnoCss from "unocss/vite";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";

export function getPluginsList(mode) {
  const lifecycle = process.env.npm_lifecycle_event;
  return [
    vue({
      script: {
        defineModel: true
      }
    }),
    // jsx、tsx语法支持
    vueJsx(),
    viteBuildInfo(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
      imports: ["vue"]
    }),
    Components({
      resolvers: [ElementPlusResolver()]
    }),
    // 自定义主题
    themePreprocessorPlugin({
      scss: {
        multipleScopeVars: genScssMultipleScopeVars(),
        // 在生产模式是否抽取独立的主题css文件，extract为true以下属性有效
        extract: false,
        // 会选取defaultScopeName对应的主题css文件在html添加link
        themeLinkTagId: "head",
        // "head"||"head-prepend" || "body" ||"body-prepend"
        themeLinkTagInjectTo: "head",
        // 是否对抽取的css文件内对应scopeName的权重类名移除
        removeCssScopeName: false
      }
    }),
    // svg组件化支持
    svgLoader(),

    //按需加载
    createStyleImportPlugin({
      resolves: [VxeTableResolve()]
    }),

    //打包分析
    lifecycle === "report"
      ? visualizer({ open: true, brotliSize: true, filename: "report.html" })
      : null,
    VueSetupExtend(),
    qiankun("wuhanCrm2", {
      useDevMode: mode !== "development"
    }),
    UnoCss()
  ];
}
