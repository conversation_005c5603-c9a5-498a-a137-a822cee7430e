<script lang="ts" setup>
import { storeToRefs } from "pinia";
import { useUserStore } from "@/store/modules/user";
import { getAppealListApi } from "@/api/order/index";
import { timeChange } from "@/utils/common/index";
import { sourceList } from "@/utils/data/index";
import { useSearch } from "@/hooks/useSearch";
import { useRouter } from "vue-router";
import MyList from "@/components/MyList/index.vue";
import { appealStatus } from "@telesale/shared/src/data/orderList";
import { getLabel } from "@telesale/shared";

const props = withDefaults(
  defineProps<{
    immediate?: boolean;
  }>(),
  {
    immediate: true
  }
);

const { userMsg } = storeToRefs(useUserStore());
const router = useRouter();

const { list, loading, finished, error, onLoad, onSearch, searchForm } =
  useSearch({
    api: getAppealListApi,
    immediate: props.immediate
  });

const goDetail = (infoUuid: string, phone: string) => {
  router.push({
    path: "/customer/details",
    query: {
      infoUuid,
      phone
    }
  });
};

defineExpose({
  onSearch,
  searchForm
});
</script>

<template>
  <div class="h-100%">
    <MyList
      v-model:error="error"
      :list="list"
      :loading="loading"
      :finished="finished"
      :onLoad="onLoad"
    >
      <template #default="{ data }">
        <div class="customer py-4">
          <div class="flex mb-20px">
            <div class="mr-40px">手机号 {{ data.phone }}</div>
            <div>洋葱ID {{ data.onionid }}</div>
          </div>
          <div class="mb-20px">订单号 {{ data.orderid }}</div>
          <div class="mb-20px">课程名称 {{ data.course }}</div>
          <div class="flex flex-wrap gap-20px">
            <div class="tag" v-if="userMsg.leafNode">
              {{ data.workerName }}
            </div>
            <div class="tag">
              {{ getLabel(data.status, appealStatus, "name", "id") }}
            </div>
            <div class="tag">支付时间 {{ timeChange(data.payTime, 2) }}</div>
          </div>
        </div>
      </template>
    </MyList>
  </div>
</template>

<style lang="scss" scoped>
.customer {
  border-bottom: 1px solid #b4b4b4;
  .tag {
    padding: 4px 10px;
    background-color: #fff9e9;
    color: #a2653a;
    font-size: 24px;
  }
}
</style>
