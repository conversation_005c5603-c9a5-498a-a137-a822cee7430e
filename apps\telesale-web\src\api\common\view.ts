/*
 * @Date         : 2024-11-18 11:45:06
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */
import { http } from "../../utils/http";
import baseURL from "../url";

export interface ViewData {
  search: any;
  tableSelect: any;
  tableSort: any;
  tableColumns: any;
}

type ViewType = "public" | "private" | "share" | "default";

export interface ViewInfo {
  id?: number;
  belong: string;
  member: number[];
  viewName: string;
  viewType: ViewType;
  parentId?: number;
  owner?: number;
  hasPermission?: boolean;
  viewData: string;
}
export interface ViewList {
  id: number;
  viewName: string;
  belong: string;
  viewType: ViewType;
  hasPermission: boolean;
}

/**
 * @description: 创建视图
 * @param {ViewInfo} data
 * @returns any
 */
export const createViewApi = (data: ViewInfo) => {
  return http.request("post", `${baseURL.api}/wuhan-datapool/view/create`, {
    data
  });
};

/**
 * @description: 获取视图
 * @param {id} number
 * @returns {ViewInfo}
 */
export const getViewApi = (data: { id?: number; lockId?: boolean }) => {
  return http.request<ViewInfo>(
    "post",
    `${baseURL.api}/wuhan-datapool/view/get`,
    {
      data
    }
  );
};
/**
 * @description: 获取视图列表
 * @param {id} number
 * @returns {ViewInfo}
 */
export const getViewListApi = (data: { belong: string }) => {
  return http.request<{
    viewList: ViewList[];
  }>("post", `${baseURL.api}/wuhan-datapool/view/list`, { data });
};

/**
 * @description: 获取视图
 * @param {ViewInfo} data
 * @returns any
 */
export const updateViewApi = (data: ViewInfo) => {
  return http.request("post", `${baseURL.api}/wuhan-datapool/view/update`, {
    data
  });
};

/**
 * @description: 删除视图
 * @param {id} number
 * @returns any
 */
export const deleteViewApi = (data: { id: number }) => {
  return http.request("post", `${baseURL.api}/wuhan-datapool/view/delete`, {
    data
  });
};

/**
 * @description: 删除视图
 * @param {id} number
 * @returns any
 */
export const createLocalViewApi = (data: {
  parentId: number;
  viewData: string;
}) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/view/create_local`,
    {
      data
    }
  );
};

/**
 * @description: 删除视图
 * @param {id} number
 * @returns any
 */
export const resetLocalViewApi = (data: { id: number }) => {
  return http.request<ViewInfo>(
    "post",
    `${baseURL.api}/wuhan-datapool/view/reset_local`,
    {
      data
    }
  );
};

/**
 * @description: 保存或者重置视图
 * @param {id} number
 * @returns any
 */
export const saveOrResetViewApi = (data: {
  id: number;
  action: "save" | "reset";
}) => {
  return http.request<ViewInfo>(
    "post",
    `${baseURL.api}/wuhan-datapool/view/alert_view_op`,
    {
      data
    }
  );
};
