import { http } from "../utils/http";
import baseURL from "./url";

//呼叫记录
export const getCallRecord = data => {
  return http.request("post", `${baseURL.api}/web/call/history/list`, {
    data
  });
};

//查看明文根据通话记录 ID 获取通话记录信息
export const lookPhone = id => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-callphone/history/takeByActionID/${id}`
  );
};

//根据对象存储地址下载通话录音
export const downloadCallRecord = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-callphone/history/setMetadata`,
    {
      params
    }
  );
};

// 查询外呼渠道
export const getListCall = () => {
  return http.request("get", `${baseURL.api}/web/channel`, {
    params: {
      pageSize: 20,
      pageIndex: 1
    }
  });
};

//外呼渠道详情
export const detailCall = params => {
  return http.request("get", `${baseURL.api}/web/channel/take/${params.id}`);
};

//添加外呼渠道
export const addCall = data => {
  return http.request("post", `${baseURL.api}/web/channel`, {
    data
  });
};

//编辑外呼渠道
export const editCall = data => {
  return http.request("put", `${baseURL.api}/web/channel`, {
    data
  });
};

//删除外呼渠道
export const delCall = params => {
  return http.request("delete", `${baseURL.api}/web/channel/${params.id}`);
};

// 释放记录列表
export const getRelease = data => {
  return http.request("post", `${baseURL.api}/web/log/release`, {
    data
  });
};

//小号列表
export const getListNumber = data => {
  return http.request("post", `${baseURL.api}/web/phone/pool/list`, {
    data
  });
};

//删除小号
export const delNumber = data => {
  return http.request("post", `${baseURL.api}/web/phone/pool/remove`, {
    data
  });
};

//新增小号
export const addNumber = data => {
  return http.request("post", `${baseURL.api}/web/phone/pool/save`, {
    data
  });
};

//工号池列表
export const getListNO = params => {
  return http.request("get", `${baseURL.api}/web/agentPool/find`, {
    params
  });
};

//创建坐席绑定工号
export const addNO = data => {
  return http.request("post", `${baseURL.api}/web/agentPool`, {
    data
  });
};

//编辑坐席绑定工号
export const editNO = data => {
  return http.request("put", `${baseURL.api}/web/agentPool`, {
    data
  });
};

//删除坐席绑定工号
export const delNO = params => {
  return http.request("delete", `${baseURL.api}/web/agentPool/${params.id}`);
};

//查询指定坐席绑定工号列表
export const getJobNO = params => {
  return http.request("get", `${baseURL.api}/web/worker/agentPool`, {
    params
  });
};

// 后台 - 坐席Excel文件解析
export const uploadAgentNO = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-callphone/agentPool/batchCreate/task/parse`,
    {
      data
    },
    {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

//后台 - 导入坐席信息
export const exportAgentNO = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-callphone/agentPool/batchCreate/task`,
    {
      data
    }
  );
};

//新版根据任务Id，查询后台异步任务
export const taskNO = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-callphone/agentPool/batchCreate/task/${params.id}`,
    {
      params
    }
  );
};

//查询注销小程序记录
export const disableAccountList = params => {
  return http.request("get", `${baseURL.api}/web/mp/user/disable/list`, {
    params
  });
};

//查看排班
export const findSchedule = data => {
  return http.request("post", `${baseURL.api}/web/schedule/list`, {
    data
  });
};

//编辑排班
export const editSchedule = data => {
  return http.request("post", `${baseURL.api}/web/schedule/save`, {
    data
  });
};

//导出排班
export const scheduleExport = data => {
  return http.request("post", `${baseURL.api}/web/schedule/export`, {
    data
  });
};

//获取渠道活码的坐席出勤列表
export const getListCode = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-miniprogram/qrcodeAttendance`,
    {
      params
    }
  );
};

//更新渠道qrcodeAttendance出勤
export const updateCode = data => {
  return http.request(
    "put",
    `${baseURL.api}/wuhan-miniprogram/qrcodeAttendance`,
    {
      data
    }
  );
};

//微服务列表
export const getListMicroservice = () => {
  return http.request("get", `${baseURL.api}/web/microservice`);
};

//路由配置列表
export const getListInterface = data => {
  return http.request("post", `${baseURL.api}/web/authorize/list`, {
    data
  });
};

//保存路由配置
export const updateInterface = data => {
  return http.request("put", `${baseURL.api}/web/authorize`, {
    data
  });
};

//删除路由配置
export const deleteInterface = id => {
  return http.request("delete", `${baseURL.api}/web/authorize/${id}`);
};

//公告列表
export const getListNotice = params => {
  return http.request("get", `${baseURL.api}/wuhan-worker/news`, {
    params
  });
};

//添加公告
export const addNotice = data => {
  return http.request("post", `${baseURL.api}/wuhan-worker/news`, {
    data
  });
};

//编辑公告
export const editNotice = data => {
  return http.request("put", `${baseURL.api}/wuhan-worker/news`, {
    data
  });
};

//公告详情
export const detailNotice = id => {
  return http.request("get", `${baseURL.api}/wuhan-worker/news/take/${id}`);
};

//获取当前发布的版本
export const getNotice = () => {
  return http.request("get", `${baseURL.api}/wuhan-worker/news/last`);
};

//删除用户授权信息
export const deleteMiniAccount = params => {
  return http.request("delete", `${baseURL.api}/wuhan-miniprogram/user`, {
    params
  });
};

//获取所有小程序
export const getMini = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-miniprogram/miniprogram/findAll`,
    {
      params
    }
  );
};

//设置某个小程序为当前使用小程序
export const setMini = id => {
  return http.request(
    "put",
    `${baseURL.api}/wuhan-miniprogram/miniprogram/using/${id}`
  );
};

// 获取小程序详情
export const getMiniInfoApi = id => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-miniprogram/miniprogram/take/${id}`
  );
};

// 新增小程序
export const addMiniApi = data => {
  return http.request("post", `${baseURL.api}/wuhan-miniprogram/miniprogram`, {
    data
  });
};

// 编辑小程序
export const updateMiniApi = data => {
  return http.request(
    "put",
    `${baseURL.api}/wuhan-miniprogram/miniprogram/${data.id}`,
    {
      data
    }
  );
};

// 解封/禁用小程序
export const setMiniStatusApi = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-miniprogram/miniprogram/setStatus`,
    {
      data
    }
  );
};

//行为列表
export const getListBehavior = () => {
  return http.request("get", `${baseURL.api}/wuhan-worker/behavior/tel_sale`);
};

//获取行为日志列表
export const getListOperateLog = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-worker/behavior/logger/tel_sale/${params.actionKey}`,
    {
      params
    }
  );
};
