import { http } from "../../utils/http";
import baseURL from "../url";
import { TagInstance } from "../AIQualityInspection/excellentCases";

export function getLibraryUUID() {
  switch (import.meta.env.MODE) {
    case "master":
      return "b5148ed7-0abb-4a9b-97c5-7f550959d709";
    case "stage":
      return "8c4945b2-40a6-44c1-b528-b016ecdef885";
    case "test":
      return "7ba1126d-56ac-4be7-b3ee-13198cd8780b";
  }

  return "7ba1126d-56ac-4be7-b3ee-13198cd8780b";
}

/**
 * @description: 获取固定知识库目录
 */
export function getLibraryCategory(params: {
  libraryId?: string;
  libraryUUID: string;
}) {
  return http.get(`${baseURL.aisaas}/web/libraryCategory/query`, { params });
}

/**
 * @description: 查询知识库的问答知识
 * @param {object} params - 请求参数
 * @param {number} [params.libraryId] - 知识库ID
 * @param {string} [params.libraryUUID] - 知识库UUID
 * @param {string[]} [params.ids] - 知识点ID列表
 * @returns {Promise<{ list: Array<{
 *   id: string;
 *   libraryId: number;
 *   knowledgeCategoryId: number;
 *   baseQuestion: string;
 *   answer: string;
 *   effective: number;
 *   expire: number;
 *   timeStatus: boolean;
 *   trainId: string;
 *   KnowledgeQuestions: Array<any>;
 *   tags: Array<any>;
 * }> }>} 知识点列表
 */
export function getMget(params: {
  libraryId?: number;
  libraryUUID?: string;
  ids?: string[];
}) {
  return http.get(`${baseURL.aisaas}/web/knowledge/mget`, { params });
}

/**
 * @description: 批量根据条件查询知识数据
 * @param {object} params - 请求参数
 * @param {number} [params.libraryId] - 知识库ID
 * @param {string} [params.libraryUUID] - 知识库UUID
 * @param {string} [params.content] - 搜索内容
 * @param {number} [params.pages] - 页码
 * @param {number} [params.pageSize] - 每页数量
 * @param {number} [params.knowledgeCategoryId] - 知识分类ID
 * @param {string} [params.searchMode] - 搜索模式
 * @returns {Promise<{
 *   list: Array<{
 *     id: string;
 *     libraryId: number;
 *     knowledgeCategoryId: number;
 *     baseQuestion: string;
 *     answer: string;
 *     effective: number;
 *     expire: number;
 *     timeStatus: boolean;
 *     trainId: string;
 *     KnowledgeQuestions: Array<any>;
 *     tags: Array<any>;
 *   }>,
 *   total: string
 * }>} 知识点列表和总数
 */
export function getQuery(params: {
  libraryId?: number;
  libraryUUID?: string;
  content?: string;
  pages?: number;
  pageSize?: number;
  knowledgeCategoryId?: number;
  searchMode?: string;
  tags?: TagInstance[];
}) {
  return http.post(`${baseURL.aisaas}/web/knowledge/query`, { data: params });
}
