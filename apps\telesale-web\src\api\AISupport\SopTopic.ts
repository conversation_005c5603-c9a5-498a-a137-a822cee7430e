import { http } from "../../utils/http";
import baseURL from "../url";

export function getSopTopicSet() {
  return http.get(`${baseURL.robot}/admin/sopTopicSet`);
}

export function updateSopTopicSet(params) {
  return http.post(`${baseURL.robot}/admin/sopTopicSet`, {
    data: params
  });
}

export function getSopTopicByUserId(userId: string) {
  return http.get(
    `${baseURL.robot}/admin/sopTopicSet/generate/${userId}`,
    {},
    {
      timeout: 99999999
    }
  );
}
