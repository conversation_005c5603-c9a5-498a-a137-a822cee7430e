<!--
 * @Date         : 2024-04-16 18:58:01
 * @Description  : 图片预览
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { ref, computed } from "vue";

const props = defineProps<{
  show: boolean;
  list: string[];
  index?: number;
}>();
const emits = defineEmits(["update:show"]);

const showViewer = computed({
  get() {
    return props.show;
  },
  set(val: boolean) {
    emits("update:show", val);
  }
});

const current = ref<number>(props.index || 0);

const switchImage = (val: number) => {
  current.value = val;
};
</script>

<template>
  <div>
    <el-image-viewer
      v-if="showViewer"
      :url-list="props.list"
      teleported
      @close="showViewer = false"
      @switch="switchImage"
      :initial-index="current"
      v-bind="$attrs"
    />
    <teleport to="body">
      <div
        class="fixed bottom-20px right-50px font-bold c-white text-20px z-9999 text-center"
        v-if="showViewer"
      >
        {{ current + 1 }} / {{ props.list.length }}
      </div>
    </teleport>
  </div>
</template>

<style lang="scss" scoped></style>
