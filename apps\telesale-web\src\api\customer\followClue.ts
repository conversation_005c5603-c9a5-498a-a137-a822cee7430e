/*
 * @Date         : 2024-05-29 10:38:22
 * @Description  : 待跟进面板相关api
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */
import { http } from "../../utils/http";
import baseURL from "../url";

export interface FollowClueReq extends PageInfo {
  /** 小组Id */
  orgId: number;
  /** 坐席Id */
  workerId: number;
  /** 洋葱Id */
  onionId: string;
  /** 客户手机号 */
  phone: string;
  /** 最小订单金额 */
  amountMin: number;
  /** 最大订单金额 */
  amountMax: number;
  /** 成交开始时间 */
  payStart: number;
  /** 成交结束时间 */
  payEnd: number;
  /** 线索开始时间 */
  clueStart: number;
  /** 线索结束时间 */
  clueEnd: number;
  /** 推送开始时间 */
  pushStart: number;
  /** 推送结束时间 */
  pushEnd: number;
  /** 根据什么字段排序"stage","deal_status","pay_time", "last_active_time", "user_expire", "push_at" */
  orderBy: string;
  /** 正序还是倒序排序"asc","desc" */
  sort: string;
  /** 面板类别 1接通未成交2正价付费非大会员3体验课 */
  followType: number;
  dealStatus: number;
  isLock: boolean;
  callCount: number;
  familyCategory?: string[];
  payTime: any[];
  createTime: any[];
  clueTime: any[];
  pushTime: any[];
  watchTime: any[];
  watchTimeStart: number;
  watchTimeEnd: number;
  openCal: number;
  timeRang: any;
  familyId: string;
  combSort: any[];
  [x: string]: any;
}

export interface FollowInfo {
  /** 线索表中的Id */
  id: number;
  /** 线索Id */
  infoUuid: string;
  /** 洋葱Id */
  onionId: string;
  /** 客户手机号 */
  phone: string;
  /** 学段 */
  stage: string;
  /** 处理结果 */
  dealStatus: number;
  /** 成交时间 */
  payTime: number;
  /** 订单Id */
  orderId: string;
  /** 课程名称 */
  goodName: string;
  /** 成交额 */
  amount: number;
  /** 最近一次看课时间 */
  lastActiveTime: number;
  /** 线索到期时间 */
  userExpire: number;
  /** 推送时间 */
  pushAt: number;
  /** 坐席名称 */
  workerId: number;
  /** 所属小组 */
  orgId: number;
}

/**
 * @description: 获取待跟进面板
 * @param {FollowClueReq} data
 * @returns {FollowClueRes}
 */
export const getFollowClueApi = (data: Partial<FollowClueReq>) => {
  return http.request<{
    followList: FollowInfo[];
    list: FollowInfo[];
    number: number;
  }>("post", `${baseURL.api}/wuhan-datapool/workerAllocateInfo/follow`, {
    data
  });
};

/**
 * @description: 获取助教推送列表
 */
export const getTeacherPushApi = (data: any) => {
  return http.request<{
    followList: any[];
    list: FollowInfo[];
  }>(
    "post",
    `${baseURL.api}/wuhan-datapool/workerAllocateInfo/upgrade_intention_follow`,
    {
      data
    }
  );
};

interface TeacherChat {
  id: number;
  workerId: number;
  uuid: string;
  userId: string;
  onionId: string;
  servicePeriod: string;
  teamId: number;
  followedBy: string;
  followedByAssist: string;
  intentDescription: string;
  hasFollowed: string;
  hasUpgraded: string;
  createdAt: string;
  chatImages: string;
  updatedAt: string;
  pushAt: string;
}

/**
 * @description: 获取助教聊天记录
 */
export const getTeacherChatApi = (params: { userId: string }) => {
  return http.request<{ userIntentionDesc: TeacherChat[] }>(
    "get",
    `${baseURL.api}/wuhan-datapool/workerAllocateInfo/get_intention_desc`,
    {
      params
    }
  );
};
