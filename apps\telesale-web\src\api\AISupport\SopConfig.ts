import { http } from "../../utils/http";
import baseURL from "../url";

export function getSopConfig(): Promise<
  ReturnValue<{
    instance: {
      id: number;
      content: string;
      updatedAt: string;
      createdAt: string;
    };
  }>
> {
  return http.get(`${baseURL.robot}/admin/sopConfig/1`);
}

export function updateSopConfig(content: string) {
  return http.post(`${baseURL.robot}/admin/sopConfig/1`, {
    data: {
      content
    }
  });
}

export function getSopConfigById(id) {
  return http.get(`${baseURL.robot}/admin/sopConfig/${id}`);
}
