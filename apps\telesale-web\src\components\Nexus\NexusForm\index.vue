<!--
 * @Date         : 2024-01-08 15:49:17
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-form
    ref="FormRef"
    :model="model"
    :label-width="labelWidth"
    @submit.prevent
  >
    <slot />
  </el-form>
</template>

<script lang="ts" setup name="NexusForm">
import { FormInstance, ElForm } from "element-plus";
import { defineModel, ref } from "vue";

import assignment from "./script/assignment";

const props = withDefaults(
  defineProps<{
    labelWidth?: string; // label宽度
  }>(),
  {
    labelWidth: "120px"
  }
);

const FormRef = ref();
const model = defineModel<Record<string, any>>();

// 验证
function validateFn(formEl: FormInstance | undefined) {
  if (!formEl) return;
  return new Promise((resolve, inject) => {
    formEl.validate((valid, fields) => {
      if (valid) {
        resolve(valid);
      } else {
        inject(Error("error submit!" + fields));
      }
    });
  });
}

async function validate() {
  await validateFn(FormRef.value);
}

defineExpose({ validate, assignment });
</script>

<style scoped lang="scss"></style>
