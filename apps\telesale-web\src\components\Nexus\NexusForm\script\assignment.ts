/**
 * @description: 对象赋值
 * @param {*} oldObj
 * @param {*} newObj
 * @param {*} initObj
 */

export default function assignment(oldObj: Object, newObj: Object, initObj: Object) {
  if (!newObj) {
    for (const key in oldObj) {
      if (Object.prototype.hasOwnProperty.call(oldObj, key)) {
        oldObj[key] = initObj[key]
      }
    }
    return
  }

  for (const key in oldObj) {
    if (Object.prototype.hasOwnProperty.call(oldObj, key)) {
      oldObj[key] = newObj[key]
    }
  }
}
