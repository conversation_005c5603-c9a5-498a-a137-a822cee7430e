/*
 * @Author: xia<PERSON><PERSON> <EMAIL>
 * @Date: 2024-11-27 16:34:00
 * @LastEditors: xiaozhen <EMAIL>
 * @LastEditTime: 2025-04-28 11:42:08
 * @FilePath: /telesale-web_v2/apps/telesale-web/src/api/statistics_rank.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { http } from "../utils/http";
import baseURL from "./url";

//个人当配转化率排行
export const rankPersonRate = data => {
  return http.request(
    "post",
    `${baseURL.api}/dashboard/aggregation/rank/payConvertAmountWithReward`,
    {
      data
    }
  );
};

//个人营收排行
export const rankPersonRevenue = data => {
  return http.request("post", `${baseURL.api}/web/ranking/list`, {
    data
  });
};

//团队或小组营收排行
export const rankRevenue = data => {
  return http.request("post", `${baseURL.api}/web/organization/ranking/list`, {
    data
  });
};

//团队或小组目标完成率排行
export const rankTarget = data => {
  return http.request("post", `${baseURL.api}/web/rank/team/goal`, {
    data
  });
};

//转介绍个人营收排行
export const rankPersonTransfer = params => {
  return http.request("get", `${baseURL.api}/web/rank/referral/worker`, {
    params
  });
};

//转介绍小组营收排行
export const rankGroupTransfer = params => {
  return http.request("get", `${baseURL.api}/web/rank/referral/group`, {
    params
  });
};
