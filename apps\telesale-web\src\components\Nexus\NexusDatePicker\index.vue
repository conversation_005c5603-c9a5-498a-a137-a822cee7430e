<!--
 * @Date         : 2023-08-25 14:23:55
 * @Description  : 通用时间组件
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-date-picker v-model="_value"
                  :type="type"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  range-separator="～"
                  :default-value="defaultValue"
                  :disabled-date="disabledDateHandle"
                  :default-time="defaultTime"
                  :shortcuts="showShortcuts ? shortcuts : []"
                  @calendar-change="calendarChange	" />
</template>

<script lang='ts' setup name="NexusDatePicker">
import { defineModel } from 'vue'
import { ElDatePicker, DatePickType } from 'element-plus'

import useDatePicker from './hooks/useDatePicker'
import useCheckDisabled from './hooks/useCheckDisabled'
import useShortcuts from './hooks/useShortcuts'

import 'element-plus/es/components/date-picker/style/css'

const props = withDefaults(defineProps<{
  limitType?: 'paramast' | 'none' | 'future'
  limitDate?: {
    num: number
    type: string
  } | null
  type?: DatePickType
  showShortcuts?: boolean // 快捷指令
}>(), {
  limitType: 'none',
  limitDate: () => {
    return {
      num: 1,
      type: 'month'
    }
  },
  showShortcuts: false,
  type: 'daterange'
})

const _value = defineModel<any>()

const { calendarChange, defaultValue, initDatePickToDay, initDatePickToCurrentMonth, initDatePickToMonth, unixmsDatePicker, unixsDatePicker, startTime } = useDatePicker(_value)
const { disabledDateHandle, defaultTime } = useCheckDisabled(props, { startTime })
const { shortcuts } = useShortcuts()

defineExpose({
  initDatePickToDay, unixmsDatePicker, unixsDatePicker, initDatePickToMonth, initDatePickToCurrentMonth
})
</script>

<style scoped>

</style>
