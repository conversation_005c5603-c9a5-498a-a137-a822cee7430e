/*
 * @Date         : 2024-11-18 18:36:56
 * @Description  : 地区分数线
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { http } from "../../utils/http";
import baseURL from "../url";

export function getAdmissionScore(params: {
  pages: number;
  pageSize: number;
  schoolName?: string;
  regionCode?: string;
  year?: string;
  city?: string;
  sortBy?: "id" | "score";
}) {
  return http.get(`${baseURL.robot}/admin/admissionScore`, { params });
}

export function createAdmissionScore(data: {
  year: string;
  score: string;
  regionCode: string;
  dataSource: string;
  schoolName: string;
  city: string;
}) {
  return http.request("put", `${baseURL.robot}/admin/admissionScore`, {
    data
  });
}

export function updateAdmissionScore(data: {
  id: string;
  year: string;
  score: string;
  regionCode: string;
  dataSource: string;
  schoolName: string;
  city: string;
}) {
  return http.post(`${baseURL.robot}/admin/admissionScore/${data.id}`, {
    data: data
  });
}

/**
 * @description: 批量导入分数线
 * @param {object} data
 */
export function importAdmissionScore(data: { fileUrl: string }) {
  return http.post(`${baseURL.robot}/admin/admissionScore/import`, {
    data: data
  });
}
