import {
  CallRecordType,
  CustomerInfo,
  LockRes,
  OrederType,
  RegionType,
  SearchForm,
  VideoRecordType
} from "@/types/customer/details";
import { http } from "@/utils/http";
import {
  GoodsConfig,
  IpadGoodInfo
} from "@telesale/shared/src/businessHooks/payPush/types";
import { AxiosResponse } from "axios";

// 客户详情
export const getCustomerInfoApi = (
  params: SearchForm
): Promise<CustomerInfo> => {
  return http.request("get", `/web/customer/detail`, {
    params
  });
};

//查询知识点
export const getKnowledgeApi = (params: {
  id: string;
}): Promise<{
  id: string;
  name: string;
}> => {
  return http.request("get", `/web/knowledge/detail`, {
    params
  });
};

//小学思维扩展课ab组
export const getThinkingApi = (params: {
  userId: string;
}): Promise<{
  isA: boolean;
}> => {
  return http.request("get", `/wuhan-datapool/info/ab/primary_school`, {
    params
  });
};

//客户所在地
export const getRegionApi = (params: {
  userid: string;
}): Promise<RegionType> => {
  return http.request("get", `/web/user/region`, {
    params
  });
};

//查询订单记录
export const getOrderListApi = (params: {
  userid: string;
}): Promise<OrederType[]> => {
  return http.request("get", `/web/customer/raw/order/list`, {
    params
  });
};

//查询用户最近观看记录
export const videoHistoryApi = (userid: string): Promise<VideoRecordType[]> => {
  return http.request("get", `/web/customer/video/history/${userid}`);
};

// 获取跟单记录
export const getCallRecordApi = (data: {
  infoUuid: string;
}): Promise<ReturnList<CallRecordType>> => {
  return http.request("post", "/scrm/customer/call/record/list", { data });
};

// 获取用户信息记录
export const getUserRecordApi = (params: {
  infoUuid: string;
}): Promise<{
  description: string;
}> => {
  return http.request("get", "/wuhan-datapool/info/GetInfoDescription", {
    params
  });
};

// 获取线索锁定状态
export const getLockListApi = (data: {
  infoUuids: string[];
}): Promise<LockRes[]> => {
  return http.request("post", "/web/customer/lock/query", { data });
};

// 取线索锁定
export const lockCueApi = (data: { infoUuid: string; familyId: string }) => {
  return http.request("post", "/web/customer/lock", { data });
};

// 线索解锁
export const unlockCueApi = (data: { infoUuid: string; familyId: string }) => {
  return http.request("post", "/web/customer/unlock", { data });
};

//补差价查询V4
export const priceDiffFind = data => {
  return http.request("post", `/wuhan-datapool/diffprice/query/v4`, {
    data,
    isData: true
  });
};

//平板续购
export const repurchaseFind = params => {
  return http.request("get", `/web/repurchase/strategy/list`, {
    params,
    isData: true
  });
};

//补差价查询-新
export const priceDiffFindNew = data => {
  return http.request("post", `/wuhan-datapool/diffprice/query`, {
    data,
    isData: true
  });
};

//推送支付订单
export const pushPay = data => {
  return http.request("post", `/web/order/push/${data.pushType}`, {
    data
  });
};

//查询商品列表
export const getLinkList = data => {
  return http.request("post", `/web/h5link/list`, {
    data,
    isData: true
  });
};

export interface PushRecordType {
  pushType: string;
  schoolYear: string;
  duration: number;
  isNewExclusiveLink: boolean;
  exchange: string;
  repurchaseType: string;
  name: string;
  orderId: string;
  payStatus: string;
  workerId: number;
  createdAt: string;
  vipType: string;
  goodType: string;
}

// 推送支付订单
export const pushPayRecordApi = params => {
  return http.request<{ list: PushRecordType[] }>(
    "get",
    `/sync-order/sync-order/pushOrder/list`,
    {
      params
    }
  );
};

export interface DiifPriceParams {
  uri?: string;
  from?: string;
  strategyType?: string;
  schoolYear?: string;
  dynamic?: boolean;
  type?: string;
  orderId?: string;
  name?: string;
  isInstallment?: number;
  installmentPayType?: string[];
  courseName?: string;
}

// 获取补差价商品二维码
export const differenceQrcodeApi = (data: DiifPriceParams) => {
  return http.request<ArrayBuffer>(
    "post",
    `/web/diffprice/qrcode`,
    {
      data
    },
    {
      responseType: "arraybuffer"
    }
  );
};

// 续购二维码
export const repurchaseQrcodeApi = (data: DiifPriceParams) => {
  return http.request<Blob>(
    "post",
    `/web/repurchase/link`,
    {
      data
    },
    {
      responseType: "arraybuffer"
    }
  );
};

//判断用户是否有转介绍的资格
export const isDownloadApi = params => {
  return http.request<{
    quality: boolean;
    promotionId: number;
    posterPath: string;
    name: string;
  }>("get", `/web/promotion/poster/quality`, {
    params
  });
};

interface UserRoleReq {
  infoUuid: string;
  role: string;
}

/**
 * @description: 修改用户身份
 * @param {LeafNodeQuery} params
 * @returns {LeafNodeRes}
 */
export const updateUserRoleApi = (data: UserRoleReq) => {
  return http.request("post", `/wuhan-datapool/workerAllocateInfo/updateRole`, {
    data
  });
};

export interface TrailOrder {
  orderId: string;
  userId: string;
  deviceLock: boolean;
  trialStartTime: string;
  trialEndTime: string;
  status: string;
}

/**
 * @description: 获取体验机信息
 * @param {userId} string
 * @returns {trialOrderInfos}
 */
export const getTrialOrderListApi = (params: { userId: string }) => {
  return http.request<{
    trialOrderInfos: TrailOrder[];
  }>("get", `/sync-order/order/get_user_trial_order_info`, {
    params
  });
};

/**
 * @description: 获取用户是否可以支持加购平板
 * @param {userId} string
 * @returns {ok} boolean
 */
export const getHasIpadApi = (params: { userId: string }) => {
  return http.request<{
    data: {
      ok: boolean;
      pads: IpadGoodInfo[];
    };
  }>(
    "get",
    `/web/pushOrder/pad-sale/join`,
    {
      params
    },
    {
      isData: true
    }
  );
};

/**
 * @description: 获取学段商品等信息
 * @param {userId} string
 * @returns {trialOrderInfos}
 */
export const getGoodConfigApi = () => {
  return http.request<
    AxiosResponse<{
      data: {
        stage: GoodsConfig[];
        msg: string;
        url: string;
      };
    }>
  >("get", `/web/pushOrder/good/config`, {
    isData: true
  });
};

export interface NewQue {
  regionCode: string;
}

/**
 * @description: 获取新题型字段
 * @param {NewQue} params
 * @returns {boolean}
 */
export const getNewQueApi = (params: NewQue) => {
  return http.request<{ isNewExamArea: boolean }>(
    "get",
    `/wuhan-datapool/workerAllocateInfo/isNewExamArea`,
    { params }
  );
};

/**
 * @description: 查看用户风险
 * @returns {boolean}
 */
export const getPhoneWhiteApi = (params: {
  userId?: string;
  phone?: string;
}) => {
  return http.request<{
    hasRisk: boolean;
  }>("get", `/wuhan-datapool/workerAllocateInfo/checkRisk`, {
    params
  });
};

export function getIframeTokenApi(params: {
  userId: string;
  [k: string]: unknown;
}) {
  return http.request<{ token: string; userId: string }>(
    "get",
    `/web/proxy/user/statistic_study_token`,
    { params }
  );
}

export interface GetSyncOrderDiscoveryPageAuthReqQuery {
  /**
   * 用户id
   */
  userId: string;
}

export interface GetSyncOrderDiscoveryPageAuthResBody {
  hasAuth: boolean;
}

/**
 * @description 查询用户是否是实验组
 * https://yapi.yc345.tv/project/1415/interface/api/118343
 * @date 2024-12-26
 * @export
 * @param {GetSyncOrderDiscoveryPageAuthReqQuery} params
 * @returns {Promise<GetSyncOrderDiscoveryPageAuthResBody>}
 */
export function getDiscoveryPageAuthApi(
  params: GetSyncOrderDiscoveryPageAuthReqQuery
) {
  return http.request<GetSyncOrderDiscoveryPageAuthResBody>(
    "get",
    `/sync-order/discovery_page/auth`,
    {
      params
    }
  );
}
