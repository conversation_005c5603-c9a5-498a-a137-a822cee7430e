# AI对练模式选择优化

## 文档内容总结

本文档描述了AI对练模式的优化需求，主要针对当前语音流模式耗时过长和机器人预设信息差问题进行改进。

**核心目标：** 增加纯文本AI对练模式，提高对练工作效率，优化机器人预设配置

**主要变更内容：**
- **新增功能：** 增加纯文本对练模式选择
- **交互优化：** 在任务选择页面增加模式选择弹窗
- **机器人优化：** 创建机器人时仅选择问题，不提供参考答案
- **界面调整：** 纯文本模式下取消AI语音合成和播放组件

**技术要点：** 前端交互优化、AI语音合成控制、机器人预设数据结构调整

---

## 一、需求概述

### 现状
目前业务验证下来，当前语音流的模式会导致一线业务耗时需要等待耗时很久，基于业务时效性要求考虑，希望增加纯文本返回对练模式。

当前在设置机器人预设过程中，由于提供给AI的参考资料模式为问题+答案，导致机器人没有信息差：会直接问道你们有没有周年庆活动/初升高衔接课包等。

### 需求目标
- 增加纯文本AI对练模式，提高对练工作效率
- 创建机器人的预设，选择问题后，取消参考答案的提交，仅提供问题名称作为参考资料

### 需求设计关键点
模式选择和对练交互

## 二、调整范围

| 功能名称 | 类型 | 补充说明 |
|---------|------|----------|
| 练习-纯文本对练 | 新增功能 | 增加纯文本对练模式 |

## 三、功能需求描述

### 任务选择

#### 我的任务
**任务选择-功能说明：**
- 点击练习按钮
- 打开AI练习模式弹窗选择

#### AI练习模式选择弹窗说明：

**默认选项：**

**语音+文本对练：**
- 该模式进行对练时，与现有交互保持一致

**纯文本对练模式：**
- 使用该模式进行对练时，在对练页面进行对练时，取消AI语音合成和返回，仅返回纯文本内容
- 仅AI侧返回取消语音返回，销售侧在进行对练时，还是需要通过发送语音进行对练
- AI侧文本返回完毕即可支持销售发言

### 对练明细：
如果模式为仅文本对练模式：
- 取消播放音频组件的展示

### 创建机器人-优化
- 选择问题时，调整为仅选择问题，列表中答案列删除展示
- 答案无需提供给机器人作为参考资料

## 四、权限说明

（待补充）

## 五、移动端兼容说明

（待补充）

## 六、相关文档

（待补充）

## 七、测试用例

（待补充）

## 八、需求复盘

### 数据详情
（待补充）

### 好的地方
（待补充）

### 不足的地方
（待补充）
