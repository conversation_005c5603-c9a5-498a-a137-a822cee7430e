<template>
  <div>
    <van-popup
      v-model:show="isShow"
      class="vanPopupRef"
      round
      position="bottom"
      :style="{ height: '80%' }"
    >
      <div class="p-2">
        <div class="flex justify-between items-center p-20px top">
          <van-icon
            style="flex: 0 0 auto"
            name="cross"
            @click="isShow = false"
          />
          <span v-if="currentName" class="truncate flex-auto"
            >当前选择-{{ currentName }}</span
          >
          <span v-else>{{ props.title }}</span>
          <span class="finish" @click="submit">完成</span>
        </div>
        <van-search
          v-model="searchVal"
          class="search"
          placeholder="请搜索课程名称或价格"
          shape="round"
          @update:model-value="filterSearch"
        />
        <div
          class="overflow-hidden overflow-y-scroll"
          :style="{ height: `${height}px` }"
        >
          <div v-for="(item, index) in props.data" :key="index">
            <h3 v-if="item.options.length > 0">{{ item.label }}</h3>

            <div v-for="good in item.options" :key="good.id">
              <div
                class="item flex justify-between p-20px"
                @click="changeVal(good)"
              >
                <span class="truncate" v-html="good[props.options.label]" />
                <van-icon
                  v-if="
                    props.options.value
                      ? good[props.options.value] == currentVal
                      : true
                  "
                  name="success"
                  class="finish"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script lang="ts" setup>
import { debounce, cloneDeep } from "lodash-es";
import { useResizeObserver } from "@vueuse/core";
import { getLabel } from "@/utils/common";

interface Options {
  label: string;
  value: string;
}

const props = withDefaults(
  defineProps<{
    value: any;
    name: string;
    data: any[];
    show: boolean;
    title: string;
    options?: Options;
  }>(),
  {
    options: () => {
      return {
        label: "name",
        value: "id"
      };
    }
  }
);

const emits = defineEmits([
  "update:show",
  "update:value",
  "update:name",
  "filterChange",
  "submit"
]);

const isShow = computed({
  get() {
    return props.show;
  },
  set(val: boolean) {
    emits("update:show", val);
  }
});

const currentVal = ref<any>(props.value);
const currentName = ref<string>(props.name);
const searchVal = ref<string>();
const height = ref<number>();

onMounted(() => {
  useResizeObserver(
    document.querySelector(".vanPopupRef") as HTMLElement,
    entries => {
      const entry = entries[0];
      const { height: domHeight } = entry.contentRect;
      const top = document.querySelector(".top")?.clientHeight || 0;
      const search = document.querySelector(".search")?.clientHeight || 0;
      height.value = domHeight - top - search - 20;
    }
  );
});

const filterSearch = debounce(val => {
  emits("filterChange", val);
}, 500);

const changeVal = (row: any) => {
  const value = row[props.options.value || "id"];
  if (value === Number(currentVal.value)) {
    currentVal.value = undefined;
    currentName.value = "";
    return;
  }
  const list: any[] = [];
  props.data.forEach(item => {
    list.push(...item.options);
  });
  currentVal.value = value;
  currentName.value = getLabel(
    currentVal.value,
    list,
    "nameInit",
    props.options.value
  );
};

const submit = () => {
  emits(
    "update:value",
    currentVal.value ? String(currentVal.value) : undefined
  );
  emits("update:name", currentName.value || "");
  emits("submit", currentVal.value);
  isShow.value = false;
};
</script>

<style lang="scss" scoped>
.finish {
  flex: 0 0 auto;
  color: $primary-color;
}
.item {
  border-bottom: 1px solid #9d9d9d;
}
</style>
