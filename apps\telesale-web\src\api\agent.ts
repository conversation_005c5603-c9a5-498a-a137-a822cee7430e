import { http } from "../utils/http";
import baseURL from "./url";

//所在地常量映射
export const getBase = () => {
  return http.request("get", `${baseURL.api}/wuhan-worker/constant/base`);
};

//列出指定小组的坐席
export const findMember = params => {
  return http.request("get", `${baseURL.api}/web/organization/worker/list`, {
    params
  });
};

// 列出指定小组的坐席
export const findMembersApi = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-worker/worker/findAllByBatchOrgID`,
    {
      data
    }
  );
};

//查询坐席列表
export const getListAgent = data => {
  return http.request("post", `${baseURL.api}/web/worker/list`, {
    data
  });
};

//查询邮箱是否存在
export const hasMail = mail => {
  return http.request("get", `${baseURL.api}/wuhan-worker/worker/mail/${mail}`);
};

//新增保存解绑坐席
export const save = data => {
  return http.request("post", `${baseURL.api}/web/worker/save`, {
    data
  });
};

//坐席详情
export const getDetail = data => {
  return http.request("post", `${baseURL.api}/web/worker/detail`, {
    data
  });
};

//获取坐席分配任务量设置信息
export const getData = params => {
  return http.request("get", `${baseURL.api}/web/admin/worker/setting`, {
    params
  });
};

//保存坐席分配任务量
export const saveDistribute = data => {
  return http.request("post", `${baseURL.api}/web/admin/worker/setting/save`, {
    data
  });
};

//查询坐席离职前的线索数
export const getDepartureCount = params => {
  return http.request("get", `${baseURL.api}/web/worker/departure/count`, {
    params
  });
};

//坐席离职
export const setDeparture = data => {
  return http.request("post", `${baseURL.api}/web/worker/departure`, {
    data
  });
};

// 后台 - 坐席Excel文件解析
export const uploadAgent = data => {
  return http.request(
    "post",
    `${baseURL.api}/web/worker/excel/parse`,
    {
      data
    },
    {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

//后台 - 导入坐席信息
export const exportAgent = data => {
  return http.request("post", `${baseURL.api}/web/worker/excel/import`, {
    data
  });
};

//新版根据任务Id，查询后台异步任务
export const taskNew = params => {
  return http.request(
    "get",
    `${baseURL.api}/web/worker/excel/detail/${params.id}`,
    {
      params
    }
  );
};

// 后台 - 坐席Excel文件解析
export const uploadAgentMail = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-worker/worker/excel/modify_email/parse`,
    {
      data
    },
    {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

//后台 - 导入坐席邮箱
export const exportAgentMail = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-worker/excel/import/modify_mail`,
    {
      data
    }
  );
};

//新版根据任务Id，查询后台异步任务
export const taskMail = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-worker/excel/modify_mail/detail/${params.id}`,
    {
      params
    }
  );
};

//添加部门
export const addOrganization = data => {
  return http.request("post", `${baseURL.api}/web/organization/add`, {
    data
  });
};

//修改部门名称
export const editOrganization = data => {
  return http.request("put", `${baseURL.api}/web/organization/update`, {
    data
  });
};

//删除部门
export const delOrganization = data => {
  return http.request(
    "delete",
    `${baseURL.api}/web/organization/remove/${data.id}`
  );
};

//向部门中添加新坐席
export const addMember = data => {
  return http.request("post", `${baseURL.api}/web/organization/worker/add`, {
    data
  });
};

//在部门中删除坐席
export const delMember = data => {
  return http.request(
    "delete",
    `${baseURL.api}/web/organization/worker/delete`,
    {
      data
    }
  );
};

// 批量转移组织架构关系
export const transferApi = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-worker/relationship/transfer`,
    {
      data
    }
  );
};

//查询成员所属部门
export const findPosition = data => {
  return http.request("post", `${baseURL.api}/web/organization/worker/query`, {
    data
  });
};

//查询部门成员和主管(支持分页)
export const findMemberPage = data => {
  return http.request(
    "post",
    `${baseURL.api}/web/organization/worker/list_with_leader`,
    {
      data
    }
  );
};

//重洋启思-用户可选角色
export const getRoleList = () => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-worker/wuhan-worker/role/find`
  );
};
