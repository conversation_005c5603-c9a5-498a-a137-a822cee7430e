<script setup lang="ts" name="CardCenterPreview">
import { computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { cardPreview } from "/@/api/system_cardTemplate";

interface Props {
  isNullCard: boolean;
  cardHeader: any;
  cardPreviewList: any[];
  update_multi: boolean;
}
interface Emits {
  (e: "cancelSelect"): void;
  (e: "update:loading", val: boolean): void;
  (e: "update:cardHeader", val: any): void;
  (e: "update:cardPreviewList", val: any[]): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const cardHeader = computed({
  get() {
    return props.cardHeader;
  },
  set(val: any) {
    emit("update:cardHeader", val);
  }
});
const cardPreviewList = computed({
  get() {
    return props.cardPreviewList;
  },
  set(val: any[]) {
    emit("update:cardPreviewList", val);
  }
});

function sendPreview() {
  ElMessageBox.confirm("确认发送预览吗", "提示", {
    confirmButtonText: "确认",
    type: "warning"
  })
    .then(() => {
      let link = transferFormat();
      const input = document.createElement("input");
      input.setAttribute("value", link);
      document.body.appendChild(input);
      input.select();
      document.execCommand("copy");
      document.body.removeChild(input);
      emit("update:loading", true);
      cardPreview({ card: link })
        .then(() => {
          ElMessage.success("发送预览成功");
          emit("update:loading", false);
        })
        .catch(() => {
          emit("update:loading", false);
        });
    })
    .catch(() => {
      ElMessage.info("已取消发送预览");
    });
}
function clearPreview() {
  ElMessageBox.confirm("确认清空预览吗", "提示", {
    confirmButtonText: "确认",
    type: "warning"
  })
    .then(() => {
      cardHeader.value = null;
      cardPreviewList.value = [];
      emit("cancelSelect");
      ElMessage.success("成功清空预览");
    })
    .catch(() => {
      ElMessage.info("已取消清空预览");
    });
}

//飞书消息卡片需要的数据格式
let card = {
  config: {
    enable_forward: true,
    update_multi: false
  },
  header: null,
  elements: []
};

function parseText(activeList) {
  let content = "";
  activeList.forEach(mark => {
    let temporary = "";
    switch (mark.type) {
      case "hr":
        temporary = "\n ---\n";
        break;
      case "atId":
        temporary = `<at id=${mark.text.slice(1)}></at>`;
        break;
      case "atAll":
        temporary = "<at id=all></at>";
        break;
      case "link":
        temporary = `[${mark.text}](${mark.url})`;
        break;
      default:
        temporary = mark.text;
    }
    if (mark.styles.indexOf("italics") > -1) {
      temporary = "*" + temporary + "*";
    }
    if (mark.styles.indexOf("bold") > -1) {
      temporary = "**" + temporary + "**";
    }
    if (mark.styles.indexOf("deleteLine") > -1) {
      temporary = "~~" + temporary + "~~";
    }
    if (mark.styles.indexOf("changeLine") > -1) {
      temporary = "\n" + temporary;
    }
    content += temporary;
  });
  return content;
}

//将中间的卡片视图转换成飞书消息卡片所需要的格式
function transferFormat() {
  if (props.update_multi) {
    card.config.update_multi = true;
  }
  if (cardHeader.value) {
    card.header = {
      title: {
        tag: "plain_text",
        content: cardHeader.value.json.activeText
      },
      template:
        cardHeader.value.json.activeColor === "white"
          ? ""
          : cardHeader.value.json.activeColor
    };
  } else {
    delete card.header;
  }
  card.elements = [];
  cardPreviewList.value.forEach(item => {
    let ele;
    switch (item.key) {
      case "markdown":
        ele = { tag: "markdown", content: parseText(item.json.activeList) };
        break;
      case "double":
        ele = { tag: "div", fields: [] };
        item.json.actions.forEach(list => {
          ele.fields.push({
            is_short: list.is_short,
            text: {
              tag: "lark_md",
              content: parseText(list.activeList)
            }
          });
        });
        break;
      case "hr":
        ele = { tag: "hr" };
        break;
      case "action_button":
        ele = {
          tag: "action",
          actions: [
            {
              tag: "button",
              text: {
                tag: "plain_text",
                content: item.json.actions[0].activeText
              },
              type: item.json.actions[0].activeColor
            }
          ]
        };
        item.json.actions[0].activeUrl &&
          (ele.actions[0].url = item.json.actions[0].activeUrl);
        if (item.json.actions[0].activeValue.length) {
          ele.actions[0].value = {};
          item.json.actions[0].activeValue.forEach(keyValue => {
            ele.actions[0].value[keyValue.key] = keyValue.value;
          });
        }
        if (
          item.json.actions[0].activeConfirmTitle &&
          item.json.actions[0].activeConfirmText
        ) {
          ele.actions[0].confirm = {
            title: {
              tag: "plain_text",
              content: item.json.actions[0].activeConfirmTitle
            },
            text: {
              tag: "plain_text",
              content: item.json.actions[0].activeConfirmText
            }
          };
        }
        break;
    }
    card.elements.push(ele);
  });
  return JSON.stringify(card);
}

defineExpose({
  transferFormat
});
</script>
<template>
  <div class="c-btn">
    <el-button type="primary" :disabled="isNullCard" @click="sendPreview"
      >向我发送预览
    </el-button>
    <el-button :disabled="isNullCard" @click="clearPreview"
      >清空预览
    </el-button>
  </div>
</template>
<style scoped lang="scss">
.c-btn {
  display: flex;
  justify-content: space-between;
  padding: 16px;
  background-color: #f5f6f7;
  position: sticky;
}
</style>
