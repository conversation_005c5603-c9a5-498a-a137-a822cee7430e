<script lang="ts" setup>
import { ref } from "vue";
import { cloneDeep } from "lodash-es";
import { useUserStore } from "@/store/modules/user";
import { storeToRefs } from "pinia";
import { ManualInputForm } from "@/types/customer/pool";
import { manualAddApi } from "@/api/customer/pool";
import { showLoadingToast, showToast } from "vant";
import { getLabel } from "@/utils/common";
import { useBackRoute } from "@/hooks/router/useBackRoute";
import MyUpload from "@/components/MyUpload/index.vue";

const { jobAgentList } = storeToRefs(useUserStore());

const { back } = useBackRoute();
const isShow = ref<boolean>(false);
const agentName = ref<string>("");

const transferWorkerName = computed(() =>
  getLabel(form.value.workerid, jobAgentList.value)
);

const form = ref<ManualInputForm>({
  phone: "",
  note: "",
  workerid:
    jobAgentList.value.length === 1 ? jobAgentList.value[0].id : undefined,
  type: "phone",
  onionId: "",
  voucher: []
});

const changeType = e => {
  form.value.phone = "";
  form.value.onionId = "";
};

const onOversize = () => {
  showToast("文件大小不能超过3M");
};

const onSubmit = () => {
  console.log(form.value);

  showLoadingToast({});
  const params: any = cloneDeep(form.value);
  params.workerid = params.workerid + "";
  params.voucher = params.voucher?.join?.(",");
  manualAddApi(params).then(() => {
    showToast("操作成功");
    setTimeout(() => {
      back(-1, ["customer"]);
    }, 1500);
  });
};
</script>

<template>
  <div>
    <van-form required class="mt-20px" label-align="right" @submit="onSubmit">
      <van-cell-group inset>
        <van-field name="radio" label="录入方式">
          <template #input>
            <van-radio-group
              v-model="form.type"
              direction="horizontal"
              icon-size="14"
              @change="changeType"
            >
              <van-radio name="phone">手机号</van-radio>
              <van-radio name="onionId">洋葱ID</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field
          v-if="form.type === 'phone'"
          v-model="form.phone"
          label="客户手机号"
          placeholder="请输入客户手机号"
          :rules="[
            { required: true, message: '请输入客户手机号', trigger: 'onBlur' },
            {
              message: '手机号格式有误',
              pattern: /^1[3-9]\d{9}$/,
              trigger: 'onBlur'
            }
          ]"
        />
        <van-field
          v-else
          v-model="form.onionId"
          label="洋葱ID"
          placeholder="请输入洋葱ID"
          :rules="[
            { required: true, message: '请输入洋葱ID', trigger: 'onBlur' },
            {
              message: '洋葱ID格式有误',
              pattern: /^\d{8,11}$/,
              trigger: 'onBlur'
            }
          ]"
        />
        <van-field
          v-model="form.note"
          rows="3"
          autosize
          label="备注"
          type="textarea"
          placeholder="请输入备注"
          :rules="[
            { required: true, message: '请输入备注', trigger: 'onBlur' }
          ]"
        />
        <van-field
          required
          readonly
          is-link
          label="图片"
          :rules="[
            { required: true, message: '请选择图片', trigger: 'onChange' }
          ]"
        >
          <template #input>
            <MyUpload
              v-model:value="form.voucher"
              :maxSize="3 * 1024 * 1024"
              :maxCount="3"
              multiple
              @oversize="onOversize"
            />
          </template>
        </van-field>
        <van-field
          v-model="transferWorkerName"
          readonly
          is-link
          label="坐席"
          placeholder="请选择坐席"
          :rules="[
            { required: true, message: '请选择坐席', trigger: 'onChange' }
          ]"
          @click="isShow = true"
        />
      </van-cell-group>
      <div style="margin: 16px">
        <van-button round block type="primary" native-type="submit">
          提交
        </van-button>
      </div>
    </van-form>
    <SearchSelect
      v-if="isShow"
      v-model:value="form.workerid"
      v-model:show="isShow"
      v-model:name="agentName"
      :data="jobAgentList"
      title="选择坐席"
      :options="{
        label: 'name',
        value: 'id'
      }"
    />
  </div>
</template>

<style lang="scss" scoped></style>
