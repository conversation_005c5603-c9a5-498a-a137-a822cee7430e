<script lang="ts" setup>
import PayPush from "../components/PayPush.vue";
import PushRecord from "../components/PushRecord.vue";

const active = ref<number>(0);
</script>

<template>
  <div>
    <van-tabs v-model:active="active">
      <van-tab title="支付推送">
        <PayPush />
      </van-tab>
      <van-tab title="推送记录">
        <PushRecord />
      </van-tab>
    </van-tabs>
  </div>
</template>

<style lang="scss" scoped></style>
