<script lang="ts" setup>
import { ref } from "vue";
import { useUserStore } from "@/store/modules/user";
import { storeToRefs } from "pinia";
import { CueForm } from "@/types/customer/action";
import { transferCustom } from "@/api/customer/action";
import { showLoadingToast, showToast } from "vant";
import { useRoute } from "vue-router";
import { getLabel } from "@/utils/common";
import { useBackRoute } from "@/hooks/router/useBackRoute";

const { jobAgentList } = storeToRefs(useUserStore());

const route = useRoute();
const { back } = useBackRoute();
const isShow = ref<boolean>(false);
const agentName = ref("");

const { infoUuid, workerId } = route.query || {};
const workerName = getLabel(parseInt(workerId as string), jobAgentList.value);

const transferWorkerName = computed(() =>
  getLabel(form.value.workerId, jobAgentList.value)
);
const agentList = computed(() =>
  jobAgentList.value.filter(item => item.id !== parseInt(workerId as string))
);

const form = ref<CueForm>({
  infoUuids: [infoUuid as string],
  note: "",
  workerId: undefined
});

const onSubmit = () => {
  showLoadingToast({});
  transferCustom(form.value).then(res => {
    showToast(res.error ? "转移失败" : "转移成功");
    !res.error &&
      setTimeout(() => {
        const route = {
          path: "/customer/pool"
        };
        back(-2, ["customer"]);
      }, 1500);
  });
};
</script>

<template>
  <div>
    <van-form
      required
      class="mt-20px"
      label-width="100px"
      label-align="right"
      @submit="onSubmit"
    >
      <van-cell-group inset>
        <van-field v-model="workerName" readonly label="当前归属坐席" />
        <van-field
          v-model="transferWorkerName"
          readonly
          is-link
          label="转给"
          placeholder="请选择坐席"
          :rules="[
            { required: true, message: '请选择坐席', trigger: 'onChange' }
          ]"
          @click="isShow = true"
        />
        <van-field
          v-model="form.note"
          rows="3"
          autosize
          label="转移原因"
          type="textarea"
          placeholder="请输入转移原因"
          :rules="[
            { required: true, message: '请输入转移原因', trigger: 'onBlur' }
          ]"
        />
      </van-cell-group>
      <div style="margin: 16px">
        <van-button round block type="primary" native-type="submit">
          提交
        </van-button>
      </div>
    </van-form>
    <SearchSelect
      v-if="isShow"
      v-model:value="form.workerId"
      v-model:show="isShow"
      v-model:name="agentName"
      :data="agentList"
      title="选择坐席"
      :options="{
        label: 'name',
        value: 'id'
      }"
    />
  </div>
</template>

<style lang="scss" scoped></style>
