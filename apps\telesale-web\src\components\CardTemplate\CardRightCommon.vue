<script setup lang="ts" name="CardRightCommon">
import { computed } from "vue";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";

interface Props {
  activeModule: any;
  cardHeader: any;
  cardPreviewList: any[];
}
interface Emits {
  (e: "update:activeModule", val: any): void;
  (e: "update:loading", val: boolean): void;
  (e: "update:cardHeader", val: any): void;
  (e: "update:cardPreviewList", val: any[]): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const activeModule = computed({
  get() {
    return props.activeModule;
  },
  set(val: any) {
    emit("update:activeModule", val);
  }
});

const cardHeader = computed({
  get() {
    return props.cardHeader;
  },
  set(val: any) {
    emit("update:cardHeader", val);
  }
});
const cardPreviewList = computed({
  get() {
    return props.cardPreviewList;
  },
  set(val: any[]) {
    emit("update:cardPreviewList", val);
  }
});
//删除在卡片视图中选中的模块
function deleteModule() {
  if (activeModule.value === "header") {
    cardHeader.value = null;
  } else {
    cardPreviewList.value.splice(activeModule.value, 1);
    !cardPreviewList.value.length && (cardHeader.value = null);
  }
  activeModule.value = "";
}

function moveSite(direction) {
  let index = activeModule.value - (direction === "top" ? 1 : -1);
  let arr = [];
  arr[1] = cardPreviewList.value[activeModule.value];
  arr[0] = cardPreviewList.value[index];
  cardPreviewList.value[index] = arr[1];
  cardPreviewList.value[activeModule.value] = arr[0];
  activeModule.value = index;
}
</script>
<template>
  <div>
    <div class="c-set-list">
      <span class="c-set-label"> 搭建工具：</span>
      <a
        href="https://open.feishu.cn/tool/cardbuilder?from=howtoguide"
        target="_blank"
        >点击此处打开链接（飞书消息卡片搭建工具地址）
      </a>
    </div>
    <div class="c-set-list">
      <span class="c-set-label"> 文档查阅：</span>
      <a
        href="https://open.feishu.cn/document/ukTMukTMukTM/uADOwUjLwgDM14CM4ATN"
        target="_blank"
        >点击此处打开链接（飞书开发文档-消息卡片）
      </a>
    </div>
    <div class="c-set-list">
      <span class="c-set-label"> emoji表情：</span>
      <a
        href="http://www.unicode.org/emoji/charts/full-emoji-list.html"
        target="_blank"
        >点击此处打开链接（复制Browser下的标签）
      </a>
    </div>
    <div class="c-set-list">
      <span class="c-set-label"> 重点说明：</span>
      <span style="color: #f56c6c">
        1、如需后台替换的内容请直接%{valueName}表示，valueName为与后台确认后的变量名
        <br />
        2、当只剩下标题和某一个模块时，删除这个模块时，会清空卡片预览，因为飞书不允许只存在标题
        <br />
        3、双击卡片预览内容模块的具体文本会进入编辑此文本状态
        <br />
        4、内容模块配置，上方的删除按钮为删除选中的内容模块，下方的删除按钮为删除选中的内容模块的子内容项
        <br />
        5、双列文本与内容模块基本一致，只是多了列的处理，主要为pc端使用，需要用预览去pc端查看实际效果
      </span>
    </div>
    <div class="c-set-list">
      <span class="c-set-label"> 位置调整：</span>
      <el-button
        type="primary"
        :icon="useRenderIcon('top')"
        circle
        plain
        v-if="typeof activeModule === 'number'"
        :disabled="activeModule === 0"
        @click="moveSite('top')"
      />
      <el-button
        type="success"
        :icon="useRenderIcon('bottom')"
        circle
        plain
        v-if="typeof activeModule === 'number'"
        :disabled="activeModule === cardPreviewList.length - 1"
        @click="moveSite('bottom')"
      />
      <el-button :icon="useRenderIcon('delete')" circle @click="deleteModule" />
    </div>
  </div>
</template>
<style scoped lang="scss">
.c-set-list {
  margin-bottom: 16px;
  display: flex;
  align-items: center;

  .c-set-label {
    width: 80px;
    flex: none;
  }
}
</style>
