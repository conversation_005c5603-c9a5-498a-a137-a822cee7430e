<script setup lang="ts">
import { ref } from "vue";
import { useUserStoreHook } from "/@/store/modules/user";
import { erCode, erCodeRepurchase } from "/@/api/customerDetails";
import AutoSelectLink from "../dialog/AutoSelectLink.vue";
import AutoCode from "../dialog/AutoCode.vue";

//权限
let authorizationMap = useUserStoreHook().authorizationMap;
let qrCode = ref(
  authorizationMap.indexOf("telesale_admin_exclusive_qrcode") > -1
);

interface Props {
  index: number;
  row: any;
  type?: string;
  repurchaseType?: string;
}

const props = defineProps<Props>();

//二维码
let imgUrl = ref("");
let isShowList = ref([]);
let isModelLink = ref<boolean>(false);
let dynamic = ref(false);
const isModel = ref<boolean>(false);

function erCodeWay(form, index, math) {
  isShowList.value[index] = true;
  imgUrl.value = "";
  math(form)
    .then(({ data }: { data: any }) => {
      isShowList.value[index] = false;
      let blob = new Blob([data], { type: "png" });
      imgUrl.value = (window.URL || window.webkitURL).createObjectURL(blob);
    })
    .catch(() => {
      isShowList.value[index] = false;
    });
}

function erCodeMath(row, index, from, dynamic = false) {
  if (props.type === "repurchase") {
    erCodeWay(
      {
        type: props.repurchaseType,
        from: from,
        orderId: row.paidOrderId,
        name: row.name
      },
      index,
      erCodeRepurchase
    );
  } else {
    erCodeWay(
      {
        uri: row.payPage,
        from: from,
        strategyType: "",
        schoolYear: "",
        dynamic: dynamic
      },
      index,
      erCode
    );
  }
}

function link() {
  isModelLink.value = true;
  dynamic.value = false;
}

function linkActive() {
  isModelLink.value = true;
  dynamic.value = true;
}
</script>

<template>
  <template v-if="qrCode">
    <el-button
      link
      type="primary"
      @click="link"
      v-if="props.row['strategyTypeName'] === '新禧商品'"
    >
      创建二维码
    </el-button>
    <template v-else>
      <el-popover width="176" trigger="click">
        <div class="d-cont" v-if="isShowList[props.index]">
          <div class="el-loading-spinner">
            <svg viewBox="25 25 50 50" class="circular">
              <circle cx="50" cy="50" r="20" fill="none" class="path" />
            </svg>
            <p class="el-loading-text">正在加载中...</p>
          </div>
        </div>
        <img v-else class="d-cont" :src="imgUrl" alt="二维码" />
        <template #reference>
          <el-button
            link
            type="primary"
            @click="erCodeMath(props.row, props.index, 'telesale', false)"
          >
            电销二维码
          </el-button>
        </template>
      </el-popover>
      <el-popover width="176" trigger="click">
        <div class="d-cont" v-if="isShowList[props.index]">
          <div class="el-loading-spinner">
            <svg viewBox="25 25 50 50" class="circular">
              <circle cx="50" cy="50" r="20" fill="none" class="path" />
            </svg>
            <p class="el-loading-text">正在加载中...</p>
          </div>
        </div>
        <img v-else class="d-cont" :src="imgUrl" alt="测试" />
        <template #reference>
          <el-button
            link
            type="primary"
            @click="erCodeMath(props.row, props.index, 'tiyanying', false)"
          >
            体验营二维码
          </el-button>
        </template>
      </el-popover>
    </template>
  </template>

  <el-button
    link
    type="primary"
    @click="linkActive"
    v-if="props.row['strategyTypeName'] === '新禧商品'"
  >
    创建动态二维码
  </el-button>
  <template v-else>
    <div>
      <el-button type="primary" link @click="isModel = true"
        >动态二维码</el-button
      >
      <Teleport to="body">
        <AutoCode
          v-if="isModel"
          v-model:value="isModel"
          :row="props.row"
          :repurchaseType="props.repurchaseType"
          :type="props.type"
        />
      </Teleport>
    </div>
  </template>
  <AutoSelectLink
    ref="linkRefs"
    v-if="isModelLink"
    v-model:value="isModelLink"
    v-model:dynamic="dynamic"
    :row="props.row"
  />
</template>
<style scoped lang="scss">
.d-cont {
  width: 150px;
  height: 150px;
}
</style>
