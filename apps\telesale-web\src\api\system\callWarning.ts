/*
 * @Date         : 2024-07-12 12:11:17
 * @Description  : 外呼行为预警提醒api
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "/@/utils/http";
import baseURL from "../url";

interface CallWarningRes {
  alarmNode: number[];
  alarmRange: AlarmRange[] | number[];
  notifyAt: number[] | AlarmRange[];
  interval: number;
}

interface AlarmRange {
  startAt?: number;
  endAt?: number;
  time?: number[];
}

/**
 * @description: 获取预警配置
 */
export const getCallWarningApi = () => {
  return http.request<CallWarningRes>(
    "get",
    `${baseURL.api}/wuhan-callphone/call_alarm/conf/get_call_alarm`
  );
};

/**
 * @description: 保存预警配置
 */
export const setCallWarningApi = (data: CallWarningRes) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-callphone/call_alarm/conf/save_call_alarm`,
    {
      data
    }
  );
};
