import { defineStore } from 'pinia'

interface AppState {
  keepAliveList: string[]
}

export const useAppStore = defineStore('app', {
  state: (): AppState => {
    return {
      keepAliveList: []
    }
  },
  actions: {
    setKeepAlive(name: string) {
      if (!this.keepAliveList.includes(name)) {
        this.keepAliveList.push(name)
      }
    },
    removeKeepAlive(names: string[]) {
      if (names?.length > 0) {
        this.keepAliveList = this.keepAliveList.filter(item => !names.includes(item))
      }
    }
  }
})
