<!--
 * @Date         : 2023-08-21 16:58:08
 * @Description  : 基于element table的封装
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div>
    <div class="table-box relative">
      <el-table
        ref="TableRef"
        v-loading="loading || tableLoading"
        :data="data"
        :summary-method="summaryMethod"
        :show-summary="showSummary"
        highlight-current-row
        :max-height="props.maxHeight"
        border
        :row-key="rowKey"
        style="width: 100%"
        @select="select"
        @select-all="selectAll"
        @selection-change="selectionChange"
        @sort-change="sortChange"
      >
        <slot :check-show="checkShow" />
      </el-table>

      <!-- 动态配置表格表头按钮 -->
      <div
        v-if="headerEditConfig"
        class="absolute top-12px right-10px w-20px h-20px z-9 cursor-pointer"
        @click="headerEditVisible = true"
      >
        <el-icon color="#999"><Setting /></el-icon>
      </div>
      <div v-if="!hidePagination" class="flexD w-100% mt-10px">
        <el-pagination
          v-model:current-page="pageNum"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 40]"
          layout="total, prev, pager, next, sizes, jumper"
          :total="total"
        />
      </div>
    </div>
    <HeaderEdit
      v-if="headerEditConfig"
      ref="HeaderEditRef"
      v-model:visible="headerEditVisible"
      v-model:key-list="showHeaderKeyList"
      :config="headerEditConfig"
    />
  </div>
</template>

<script lang="ts" setup name="NexusTable">
import { ElTable, ElIcon, ElPagination, vLoading } from "element-plus";
import { ref, computed } from "vue";
import { useTable } from "./hooks/useTable";
import HeaderEdit from "./HeaderEdit/index.vue";
import { useHeaderEdit, HeaderEditConfig } from "./HeaderEdit/useHeaderEdit";
import { Setting } from "@element-plus/icons-vue";

const { HeaderEditRef, headerEditVisible, checkShow, showHeaderKeyList } =
  useHeaderEdit();

const props = withDefaults(
  defineProps<{
    getList: Function;
    loading?: boolean;
    tableDataParam?: any; // 传入到useTable的参数
    unMounted?: boolean; // 是否不自动加载
    hidePagination?: boolean;
    summaryMethod?: any; // 合计行配置函数
    showSummary?: boolean; // 是否显示合计行
    dataChangeCb?: Function;
    dataKey?: string; // list/data
    pagesKey?: string; // pages/pageIndex
    maxHeight?: number | string | undefined;
    restrict?: boolean; // 10000条限制
    headerEditConfig?: HeaderEditConfig | null; // 动态表格配置
    sortChange?: any;

    rowKey?: string;
    dataFormat?: Function;
    selectionChange?: any;

    totalKey?: string;

    resFormat?: (res: any) => any;
  }>(),
  {
    loading: false,
    summaryMethod: null,
    showSummary: false,
    tableDataParam: {},
    dataChangeCb: () => {},
    dataKey: "list",
    pagesKey: "pages",
    maxHeight: undefined,
    restrict: false,
    unMounted: false,
    headerEditConfig: null,
    sortChange: () => {},

    rowKey: "id",
    dataFormat: data => data,
    selectionChange: () => {},

    totalKey: "total",

    resFormat: res => res
  }
);

const emits = defineEmits(["select", "select-all"]);

const tableOption = {
  dataKey: props.dataKey,
  pagesKey: props.pagesKey,
  restrict: props.restrict,
  unMounted: props.unMounted,
  dataChangeCb: props.dataChangeCb,
  resFormat: props.resFormat,
  ...props.tableDataParam
};

if (props.hidePagination) {
  // 不分页 直接获取所有数据
  tableOption.pageSize = 99999;
}

const {
  pageNum,
  pageSize,
  resetTable,
  info,
  updateList,
  searchHandle,
  addPar,
  clearTabel,
  tableLoading,
  tableParam
} = useTable(props.getList, tableOption);

const data = computed(() => {
  return props.dataFormat(info.value[props.dataKey]);
});

const TableRef = ref();

function select(selection, row) {
  emits("select", selection, row);
}

function selectAll(selection) {
  emits("select-all", selection);
}

const total = computed(() => {
  if (props.totalKey.includes(".")) {
    const target = props.totalKey
      .split(".")
      .reduce((acc, key) => acc?.[key], info.value);
    return Number(target);
  }
  return Number(info.value[props.totalKey]);
});

defineExpose({
  resetTable,
  updateList,
  addPar,
  clearTabel,
  searchHandle,
  info,
  TableRef,
  update: updateList,
  search: searchHandle,
  params: tableParam,
  total
});
</script>

<style scoped>
.table-box :deep(thead tr th) {
  background: var(--el-fill-color-light) !important;
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  width: 100%;
}

.total {
  :deep(td) {
    background: var(--el-fill-color-light) !important;
  }
}
</style>
