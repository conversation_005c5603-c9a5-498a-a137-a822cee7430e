<script lang="ts" setup>
import { computed, ref, reactive } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { erCode } from "/@/api/customerDetails";
import { useAppStoreHook } from "/@/store/modules/app";
import { useLinkLists } from "/@/views/customer/link/utils/linkLists";
import { useQrcode } from "/@/hooks/useQrdcode";
import ErCodeDown from "/@/components/ErCodeDown/index.vue";

let { schoolYearList } = useLinkLists();

interface Props {
  value: boolean;
  row: any;
  dynamic: boolean;
}
interface Emits {
  (e: "update:value", val: boolean): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

function handleClose() {
  isModel.value = false;
}

let loading = ref<Boolean>(false);
let imgUrl = ref<string>("");
let device = useAppStoreHook().device;
// const { qrcodeValue, qrcodeList, qrcodeType } = useQrcode();
const ruleFormRef = ref<FormInstance>();
const form = reactive({
  schoolYear: "",
  from: "telesale"
});
const rules = reactive<FormRules>({
  schoolYear: [
    {
      required: true,
      message: "请选择年级",
      trigger: "change"
    }
  ]
  // from: [
  //   {
  //     required: true,
  //     message: "请选择项目组",
  //     trigger: "change"
  //   }
  // ]
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(valid => {
    if (valid) {
      loading.value = true;
      // qrcodeType.value = form.from;
      erCode({
        uri: props.row.payPage,
        from: form.from,
        strategyType: props.row.strategyType,
        schoolYear: form.schoolYear,
        dynamic: props.dynamic,
        courseName: props.row?.cloneName || props.row?.strategyName || ""
      })
        .then(({ data }: { data: any }) => {
          let blob = new Blob([data], { type: "png" });
          imgUrl.value = (window.URL || window.webkitURL).createObjectURL(blob);
          loading.value = false;
          loading.value = false;
          ElMessage.success("操作成功");
        })
        .catch(() => {
          loading.value = false;
        });
    } else {
      return false;
    }
  });
};
</script>

<template>
  <el-dialog
    title="创建补差价二维码"
    v-model="isModel"
    :before-close="handleClose"
    append-to-body
  >
    <div v-if="imgUrl" style="text-align: center; padding-bottom: 10px">
      <ErCodeDown :imgUrl="imgUrl" />
    </div>
    <el-form
      v-else
      :model="form"
      label-suffix="："
      :label-width="device !== 'mobile' ? '140px' : ''"
      ref="ruleFormRef"
      :class="{ mobile: device === 'mobile' }"
      :rules="rules"
      v-loading="loading"
    >
      <el-row>
        <el-col :lg="4" />
        <el-col :lg="16">
          <!-- <el-form-item prop="from" label="项目组">
            <el-select
              v-model="form.from"
              placeholder="请选择项目组"
              clearable
              filterable
            >
              <el-option
                v-for="(item, index) in qrcodeList"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item> -->
          <el-form-item prop="schoolYear" label="年级">
            <el-select
              v-model="form.schoolYear"
              placeholder="请选择年级"
              clearable
              filterable
            >
              <el-option
                v-for="(item, index) in schoolYearList"
                :key="index"
                :label="item.text"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="4" />
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="submitForm(ruleFormRef)" v-if="!imgUrl">
        生成二维码
      </el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>
