<script lang="ts" setup name="customerHome">
const active = ref<"customer" | "order">("customer");
</script>

<template>
  <div>
    <template v-if="active === 'customer'">
      <van-cell title="客户池" is-link to="/customer/pool" />
      <van-cell title="已成交" is-link to="/customer/done" />
      <van-cell
        title="专属链接（新）"
        is-link
        to="/customer/newExclusiveLink"
      />
      <van-cell title="专属链接" is-link to="/customer/exclusiveLink" />
    </template>
    <template v-if="active === 'order'">
      <van-cell title="订单列表" is-link to="/order/list" />
      <van-cell title="申诉列表" is-link to="/order/appeal" />
    </template>

    <div>
      <van-tabbar v-model="active">
        <van-tabbar-item name="customer" icon="friends-o">
          客户管理
        </van-tabbar-item>
        <van-tabbar-item name="order" icon="orders-o">订单管理</van-tabbar-item>
      </van-tabbar>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
