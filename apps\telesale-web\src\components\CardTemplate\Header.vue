<script setup lang="ts" name="Header">
import { computed } from "vue";

interface Props {
  cardHeader: any; //初始化数据
}
interface Emits {
  (e: "update:cardHeader", val: any): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const cardHeader = computed({
  get() {
    return props.cardHeader;
  },
  set(val: any) {
    emit("update:cardHeader", val);
  }
});

let styleSet = {
  white: { bgColor: "#fff", color: "#1f2329" },
  blue: { bgColor: "#e1eaff", color: "#245bdb" },
  wathet: { bgColor: "#d9f3fd", color: "#037eaa" },
  turquoise: { bgColor: "#d5f6f2", color: "#078372" },
  green: { bgColor: "#d9f5d6", color: "#237b19" },
  yellow: { bgColor: "#faf1d1", color: "#aa7803" },
  orange: { bgColor: "#feead2", color: "#de7802" },
  red: { bgColor: "#fde2e2", color: "#d83931" },
  carmine: { bgColor: "#fdddef", color: "#c71077" },
  violet: { bgColor: "#f8def8", color: "#b220b2" },
  purple: { bgColor: "#ece2fe", color: "#6425d0" },
  indigo: { bgColor: "#e0e2fa", color: "#2933c7" },
  grey: { bgColor: "#8f959e", color: "#fff" }
};
</script>
<template>
  <div
    class="c-header"
    :class="{ padBot: cardHeader.json.activeColor === 'white' }"
    :style="{ backgroundColor: styleSet[cardHeader.json.activeColor].bgColor }"
  >
    <div
      class="c-header-text"
      :style="{ color: styleSet[cardHeader.json.activeColor].color }"
    >
      {{ cardHeader.json.activeText }}
    </div>
  </div>
</template>
<style scoped lang="scss">
.c-header {
  padding: 12px;
  background: #fff
    url("../../../src/assets/feishuCardImage/card-title-mask.90645943.png") 0 0
    no-repeat;
  background-size: 605px 140px;
  font-weight: 600;
  line-height: 24px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;

  &.padBot {
    padding-bottom: 0;
  }

  .c-header-text {
    font-size: 16px;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
    color: #1f2329;
  }
}
</style>
