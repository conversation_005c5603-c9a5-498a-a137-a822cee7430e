<template>
  <van-field
    v-model="text"
    v-bind="$attrs"
    readonly
    is-link
    @click="show = true"
  />
  <!-- 弹出层 -->
  <van-popup v-model:show="show" position="bottom" round @close="confirmOn">
    <van-picker
      ref="picker"
      v-model="selectedValues"
      title="请选择时间"
      :columns="columns"
      @cancel="cancelOn"
      @confirm="onConfirm"
    />
  </van-popup>
</template>

<script lang="ts" setup>
import {
  getMonthList,
  getYearList,
  getDayList,
  hList,
  mList,
  sList
} from "./data";

const props = defineProps<{
  value: any;
  defaultTime?: string;
}>();

const emits = defineEmits(["update:value"]);

const show = ref<boolean>(false);
const columns = ref<any[]>([]);
const selectedValues = ref<number[]>([]);
const text = computed({
  get() {
    return props.value;
  },
  set(val: string) {
    emits("update:value", val);
  }
});

const getcolumns = (year: number, month: number) => {
  const yearList = getYearList();
  const monthList = getMonthList();
  const dayList = getDayList(year, month);
  columns.value = [yearList, monthList, dayList, hList, mList, sList];
};

const init = () => {
  const date = props.value
    ? new Date(props.value)
    : props.defaultTime
    ? new Date(props.defaultTime)
    : new Date();
  selectedValues.value = [
    date.getFullYear(),
    date.getMonth() + 1,
    date.getDate(),
    date.getHours(),
    date.getMinutes(),
    date.getSeconds()
  ];
  getcolumns(date.getFullYear(), date.getMonth() + 1);
};
init();

// 关闭弹框
const confirmOn = () => {
  show.value = false;
};

//时间选择器关闭 值不改变并关闭弹框
const cancelOn = ({ selectedValues }) => {
  confirmOn();
};

watch(
  () => selectedValues.value,
  (n, o) => {
    if (n[0] !== o[0] || n[1] !== o[1]) {
      getcolumns(n[0], n[1]);
    }
  }
);

const addZero = (number: number) => {
  return number > 9 ? number : "0" + number;
};

// 时间选择器确定 值改变
const onConfirm = ({ selectedValues }) => {
  let endval =
    addZero(selectedValues[0]) +
    "-" +
    addZero(selectedValues[1]) +
    "-" +
    addZero(selectedValues[2]) +
    " " +
    addZero(selectedValues[3]) +
    ":" +
    addZero(selectedValues[4]) +
    ":" +
    addZero(selectedValues[5]);
  confirmOn();
  text.value = endval;
};
</script>
