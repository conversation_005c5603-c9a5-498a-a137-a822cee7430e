<script lang="ts" setup>
import { CustomerInfo } from "@/types/customer/details";
import { timeChange, getLabel } from "@/utils/common";
import { sourceList } from "@/utils/data";
import { updateUserRoleApi } from "@/api/customer/details";
import {
  notNewQuestions,
  roleList,
  sourceDetailMap
} from "@telesale/shared/src/data/customer";
import MyPicker from "@/components/MyPicker/index.vue";
import { closeToast, showLoadingToast } from "vant";

const props = defineProps<{
  data?: CustomerInfo;
  isNewQue: boolean;
}>();
const emits = defineEmits(["success"]);

const customerTypeList = [
  {
    value: 1,
    label: "新增"
  },
  {
    value: 2,
    label: "付费"
  }
];

const role = computed<string>({
  get() {
    return props.data?.role || "";
  },
  set(val: string) {
    showLoadingToast({});
    updateUserRoleApi({
      role: val,
      infoUuid: props.data!.infoUuid
    })
      .then(() => {
        emits("success");
      })
      .finally(() => {
        closeToast();
      });
  }
});
const isShow = ref<boolean>(false);

const cue = computed(() => {
  return props.data?.videoid
    ? "观看教材同步课视频"
    : props.data?.topicId
    ? "观看教材同步课视频-提交习题"
    : props.data?.triggerEvent?.practiceScene
    ? props.data?.triggerEvent?.practiceType +
      "-" +
      props.data?.triggerEvent?.practiceScene
    : props.data?.triggerEvent?.practiceType;
});

const sourceDetail = computed(() => {
  const { source, sourceDetail } = props.data || {};
  return sourceList[source!] + (sourceDetailMap[sourceDetail!] || "");
  // if (source !== "referral" && sourceDetail !== "lotteryDrawFission") {
  //   return sourceList[source!];
  // } else {
  //   return sourceList[source!] + "-抽奖";
  // }
});
</script>

<template>
  <div>
    <van-cell-group>
      <van-cell title="客户昵称" :value="props.data?.nickname" />
      <van-cell title="所在地" :value="props.data?.region">
        <template #value>
          <div
            :class="[
              'flex',
              props.isNewQue ? 'justify-between' : 'justify-end'
            ]"
          >
            <span>{{ props.data?.region }}</span>
            <span class="d-think-show min-w-100px" v-if="props.isNewQue">
              新题型
            </span>
          </div>
        </template>
      </van-cell>
      <van-cell title="用户手机号" :value="props.data?.phone" />
      <van-cell title="年级" :value="props.data?.grade" />
      <van-cell title="用户洋葱ID" :value="props.data?.onionid" />
      <van-cell title="注册时间" :value="timeChange(props.data?.regTime, 1)" />
      <van-cell
        title="是否为会员"
        :value="props.data?.isVIP === 1 ? '是' : '否'"
      />
      <van-cell
        title="客户类型"
        :value="getLabel(props.data?.usertype, customerTypeList)"
      />
      <van-cell
        title="思维拓展"
        :value="props.data?.hasThinking ? '有' : '无'"
      />
      <!-- <van-cell title="身份" link :value="getLabel(props.data?.role, roleList)">
        <template #right-icon>
          <van-icon
            name="edit"
            size="20"
            class="ml-20px"
            @click="isShow = true"
          />
        </template>
      </van-cell> -->
      <MyPicker
        v-model:show="isShow"
        v-model:value="role"
        :columns="roleList"
      />
      <van-cell title="来源" :value="sourceDetail" />
      <van-cell
        title="最近一次看课时间"
        :value="timeChange(props.data?.lastActiveTime, 2)"
      />
      <van-cell title="线索触发场景" :value="cue" />
      <van-cell
        title="知识点"
        :value="props.data?.videoid ? props.data?.knowledge : ''"
      />
      <van-cell
        title="录入备注"
        v-if="props.data?.note"
        :value="props.data?.note"
      />
    </van-cell-group>
  </div>
</template>

<style lang="scss" scoped>
.d-think-show {
  color: #e6a23c;
  font-weight: bold;
}
</style>
