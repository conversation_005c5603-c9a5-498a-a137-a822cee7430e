/*
 * @Author: xia<PERSON>hen <EMAIL>
 * @Date: 2024-11-27 16:34:00
 * @LastEditors: xiaozhen <EMAIL>
 * @LastEditTime: 2025-05-22 15:57:38
 * @FilePath: /telesale-web_v2/apps/telesale-mobile/src/api/customer/done.ts
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
import { CustomerList, SearchForm } from "@/types/customer/pool";
import { http } from "@/utils/http";

export interface DoneUserRes extends CustomerList {
  orderid: string;
  good: {
    name: string;
  };
  amount: number;
}
// 已成交列表
export const getDoneListApi = (
  data: SearchForm
): Promise<ReturnList<DoneUserRes>> => {
  return http.request<ReturnList<DoneUserRes>>(
    "post",
    `/web/customer/buy/list`,
    {
      data
    }
  );
};

//下载海报
export const downloadApi = (params: {
  promotionId: number;
  workerId: number;
  userId: string;
  platformId: number;
}) => {
  return http.request<{
    url: string;
  }>("get", `/web/promotion/poster/download`, {
    params
  });
};
