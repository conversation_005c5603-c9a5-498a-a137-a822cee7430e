/*
 * @Date         : 2025-01-23 15:15:54
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */
import { http } from "../../utils/http";
import baseURL from "../url";
import QS from "qs";

export interface CourseListCourseReqQuery {
  /**
   * 课程名称
   */
  name?: string;
  /**
   * 课程类型
   */
  courseType?: string;
  /**
   * 课程标签
   */
  courseTags?: string;
  /**
   * 是否开启仅部分成员可见
   */
  limitedUse?: string;
  /**
   * 白名单节点
   */
  whiteNode?: string;
  /**
   * 课程金额
   */
  amount?: string;
  /**
   * 分页：最大条目数
   */
  pageSize?: string;
  /**
   * 分页：页码
   */
  pageIndex?: string;
}

export type CourseInfo = {
  courseId?: string;
  name?: string;
  courseType?: string;
  courseCreatedAt?: string;
  operator?: string;
  status?: string;
  courseTags?: number[];
  limitedUse?: number;
  whiteNode?: number[];
  amount?: number;
}[];

export interface CourseListCourseResBody {
  list: CourseInfo[];
  total: number;
}

/**
 * @description 获取课程列表
 * https://yapi.yc345.tv/project/2519/interface/api/120834
 * <AUTHOR>
 * @date 2025-01-23
 * @export
 * @param {CourseListCourseReqQuery} params
 * @returns {Promise<CourseListCourseResBody>}
 */
export const getCourseListApi = (params: CourseListCourseReqQuery) => {
  return http.request<CourseListCourseResBody>(
    `get`,
    `${baseURL.api}/sync-order/course/ListCourse`,
    {
      params
      // paramsSerializer: function (params) {
      //   return QS.stringify(params, { indices: false });
      // }
    }
  );
};

/**
 * @description 获取课程列表
 * <AUTHOR>
 * @date 2025-01-23
 * @export
 * @param {string} params.id
 * @returns {Promise<CourseInfo>}
 */
export const getCourseInfoApi = (params: { id: number }) => {
  return http.request<CourseInfo>(
    `get`,
    `${baseURL.api}/sync-order/course/GetCourse`,
    {
      params
    }
  );
};

export interface CourseLinkReqBody {
  id?: string;
  from?: string;
  dynamic?: string;
  installmentPayType?: string;
  courseName?: string;
}

/**
 * @description 二维码
 * https://yapi.yc345.tv/project/2519/interface/api/120799
 * <AUTHOR>
 * @date 2025-01-23
 * @export
 * @param {CourseLinkReqBody} data
 */
export const createCourseLinkApi = (data: CourseLinkReqBody) => {
  return http.request(
    `post`,
    `${baseURL.api}/sync-order/course/generateCourseQRCode`,
    {
      data
    },
    {
      responseType: "blob"
    }
  );
};

/**
 * @description 创建商品
 * <AUTHOR>
 * @date 2025-01-23
 * @export
 * @param {CourseInfo} data
 */
export const createGoodsApi = (data: CourseInfo) => {
  return http.request(`post`, `${baseURL.api}/sync-order/course/CreateCourse`, {
    data
  });
};

/**
 * @description 删除商品
 * <AUTHOR>
 * @date 2025-01-23
 * @export
 * @param {number} id
 */
export const delGoodsApi = (params: { id: number }) => {
  return http.request(
    `delete`,
    `${baseURL.api}/sync-order/course/DeleteCourse`,
    {
      params
    }
  );
};

/**
 * @description 同步商品
 * <AUTHOR>
 * @date 2025-01-23
 * @export
 * @param {number} id
 */
export const syncGoodsApi = (data: { id: number }) => {
  return http.request(`post`, `${baseURL.api}/sync-order/course/SyncCourse`, {
    data
  });
};

/**
 * @description 更新商品
 * https://yapi.yc345.tv/project/2519/interface/api/120848
 * <AUTHOR>
 * @date 2025-01-23
 * @export
 * @param {CourseInfo} data
 */
export const updataGoodsApi = (data: CourseInfo) => {
  return http.request(`put`, `${baseURL.api}/sync-order/course/UpdateCourse`, {
    data
  });
};

export interface SyncLinkConfigReqQuery {
  /**
   * 用户id
   */
  userId?: string;
}

export interface SyncLinkConfigResBody {
  /**
   * 配置列表
   */
  data: {
    /**
     * 年级，1-4年级使用四年级，其他一一对应
     */
    schoolYear?: string;
    /**
     * 排序值，crm可能用不到
     */
    index?: number;
    /**
     * 包含的商品数据
     */
    data?: {
      /**
       * 唯一标识
       */
      id?: string;
      /**
       * 商品类型，为固定值：page998
       */
      category?: string;
      /**
       * 售卖类型，首购：first，升单：upgrade，加购：extra
       */
      saleType?: string;
      /**
       * 展示出来的名称
       */
      strategyName?: string;
      /**
       * 对应的组合商品id
       */
      skuGoodId?: string;
      /**
       * 售卖链接
       */
      payPage?: string;
      /**
       * 关联的打包商品
       */
      packGood?: {
        /**
         * 组合商品id
         */
        skuGoodId?: string;
        /**
         * 售卖链接
         */
        payPage?: string;
        /**
         * 唯一标识
         */
        id?: string;
      }[];
    }[];
  }[];
}

/**
 * @description 会场配置
 * https://yapi.yc345.tv/project/2519/interface/api/122355
 * <AUTHOR>
 * @date 2025-02-21
 * @export
 * @param {SyncLinkConfigReqQuery} params
 * @returns {Promise<SyncLinkConfigResBody>}
 */
export const getSyncDataApi = (params: SyncLinkConfigReqQuery) => {
  return http.request<SyncLinkConfigResBody>(
    `get`,
    `${baseURL.api}/sync-order/course/getBlockGoodsConfig`,
    {
      params
    }
  );
};

export interface GoodInfoReqBodyOther {
  /**
   * 用户ID
   */
  userId?: string;
  /**
   * 配置唯一标识。配置中的packGood不为空，传packGood中的id
   */
  id: string[];
}

export interface GoodInfoResBody {
  data: {
    /**
     * 组合商品id
     */
    skuGoodId: string;
    /**
     * 补差策略id，不能参与补差的时候没有此字段（crm不需要关心）
     */
    diffPriceStrategyId?: string;
    /**
     * 抵扣详情（crm用不到）
     */
    deductContent: {
      id: string;
      name: string;
      deductAmount: number;
      deductCategory: string;
      endTime: string;
      [k: string]: unknown;
    }[];
    /**
     * 抵扣金额
     */
    deductAmount: number;
    /**
     * 允许的最大抵扣金额
     */
    maxDeductAmount: number;
    /**
     * 商品售卖价
     */
    amount: number;
    /**
     * 商品划线价
     */
    originalAmount: number;
    /**
     * 对应的传参的唯一标识
     */
    id: string;
    [k: string]: unknown;
  }[];
  [k: string]: unknown;
}

/**
 * @description 会场商品详情
 * https://yapi.yc345.tv/project/2519/interface/api/122362
 * <AUTHOR>
 * @date 2025-02-21
 * @export
 * @param {GoodInfoReqBodyOther} data
 * @returns {Promise<GoodInfoResBody>}
 */
export const getGoodInfoApi = (data: GoodInfoReqBodyOther) => {
  return http.request<GoodInfoResBody>(
    `post`,
    `${baseURL.api}/sync-order/course/getBlockGoodsDetail`,
    {
      data
    }
  );
};

export interface createQrcodeReqBodyOther {
  /**
   * 会场链接
   */
  url: string;
  /**
   * 商品名称
   */
  courseName: string;
  /**
   * 是否分期支付
   */
  installmentPayType: string;
  /**
   * 是否启用动态域名
   */
  dynamic: boolean;
}

/**
 * @description 商品二维码
 * https://yapi.yc345.tv/project/2519/interface/api/122369
 * <AUTHOR>
 * @date 2025-02-21
 * @export
 * @param {createQrcodeReqBodyOther} data
 */
export const createNewQrcodeApi = (data: createQrcodeReqBodyOther) => {
  return http.request<ArrayBuffer>(
    `post`,
    `${baseURL.api}/sync-order/course/generateBlocksGoodQRCode`,
    {
      data
    },
    {
      responseType: "arraybuffer"
    }
  );
};
