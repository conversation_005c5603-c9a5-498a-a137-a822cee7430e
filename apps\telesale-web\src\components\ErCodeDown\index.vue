<!--
 * @Date         : 2024-06-27 15:18:10
 * @Description  : 二维码展示和下载
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import { useRenderIcon } from "../ReIcon/src/hooks";
import { downloadImage } from "/@/utils/common";

const props = defineProps<{
  imgUrl: string;
}>();

const download = () => {
  downloadImage(props.imgUrl, "erCode.png");
};
</script>

<template>
  <div class="relative">
    <div class="mb-10px">
      <el-button
        type="primary"
        :icon="useRenderIcon('download')"
        @click="download()"
      >
        下载
      </el-button>
    </div>
    <div>
      <img style="width: 18vw; height: auto" :src="props.imgUrl" alt="二维码" />
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
