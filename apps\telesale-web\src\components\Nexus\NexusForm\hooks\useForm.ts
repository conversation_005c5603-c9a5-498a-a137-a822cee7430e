import type {  FormInstance } from 'element-plus'
import { ref } from 'vue'

export default function() {
  const formLoading = ref(false)

  async function validateForm(formEl: FormInstance | undefined, fn: () => any) {
    if (formLoading.value) return

    formLoading.value = true
    await validateFormHandle(formEl, fn)
    formLoading.value = false
  }


  return {
    validateFn, validateForm, formLoading
  }
}


/**
 * form表单验证函数
 * @param formEl form的ref对象
 * @param fn 验证成功回调函数
 */
async function validateFormHandle(formEl: FormInstance | undefined, fn: () => any) {
  if (!formEl) return
  try {
    await validateFn(formEl)
  } catch (error) {
    return error
  }
  await fn()
}

function validateFn(formEl: FormInstance | undefined) {
  if (!formEl) return
  return new Promise((resolve, inject) => {
    formEl.validate((valid, fields) => {
      if (valid) {
        resolve(valid)
      } else {
        inject(Error('error submit!' + fields))
      }
    })
  })
}

