<!--
  * @Date         : 2025-02-18 16:44:05
  * @Description  :
  * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<template>
  <iframe
    ref="IframeRef"
    width="100%"
    :src="url"
    height="667"
    frameborder="0"
  />
</template>

<script lang="ts" setup>
import { watch, ref, onMounted, onUnmounted } from "vue";
import { showToast } from "vant";

const props = defineProps<{
  token: string;
  userId: string;
}>();
const omvd = Math.random().toString(36).substring(2, 12);
const IframeRef = ref();

const url = ref(
  `${
    import.meta.env.VITE_7to12_HOST
  }/onion-learning/study-report/analyzeReport/?token=${
    props.token
  }&channel=crm&fromPageName=crm&userId=${props.userId}&omvd=${omvd}`
);

watch(
  () => props.token,
  value => {
    if (value) {
      url.value = `${
        import.meta.env.VITE_7to12_HOST
      }/onion-learning/study-report/analyzeReport/?token=${value}&channel=crm&fromPageName=crm&userId=${
        props.userId
      }&omvd=${omvd}`;
    }
  }
);

async function listenPostMessage(event) {
  if (event.origin.indexOf("yangcongxing") !== -1) {
    const { type, blob } = event.data;

    if (type === "copyText" || type === "copyImage") {
      const item = new window.ClipboardItem(blob);

      try {
        await navigator.clipboard.write([item]);
        showToast("复制成功");
      } catch (error) {
        console.error(error);
        showToast("暂不支持复制，请前往PC端CRM复制");
      }
    }
  }
}
onMounted(() => {
  window.addEventListener("message", listenPostMessage);
});
onUnmounted(() => {
  window.removeEventListener("message", listenPostMessage);
});
</script>
