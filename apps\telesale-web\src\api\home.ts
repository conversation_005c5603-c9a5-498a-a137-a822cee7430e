import { http } from "../utils/http";
import baseURL from "./url";

//营收
export const revenue = data => {
  return http.request("post", `${baseURL.api}/web/analy/order/list`, {
    data
  });
};

//线索转化
export const consume = data => {
  return http.request("post", `${baseURL.api}/web/homepage/consume`, {
    data
  });
};

//营收排行
export const revenueRank = () => {
  return http.request("get", `${baseURL.api}/web/homepage/order/rank`);
};

//转化率排行
export const rateRank = () => {
  return http.request("get", `${baseURL.api}/web/homepage/convert_rate/rank`);
};
