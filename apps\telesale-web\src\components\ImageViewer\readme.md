# 图片预览组件

## 介绍
用于预览图片列表，支持切换图片和关闭预览功能。

## 引入

```js
import ImageViewer from '/@/components/ImageViewer/index.vue';
```

## 基础用法

```tsx
<template>
  <div>
    <button @click="showViewer = true">打开图片预览</button>
    <ImageViewer v-if="showViewer" v-model:show="showViewer" :list="imageList" :index="currentIndex" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import ImageViewer from '/@/components/ImageViewer/index';

const showViewer = ref(false);
const imageList = ref([
  'https://example.com/image1.jpg',
  'https://example.com/image2.jpg',
  'https://example.com/image3.jpg'
]);
const currentIndex = ref(0);
</script>
```

## Api

### Props

| 参数    | 说明                                                         | 类型      | 默认值  |
| ------- | ------------------------------------------------------------ | --------- | ------- |
| v-model:show    | 控制图片预览组件的显示与隐藏，支持双向绑定                   | boolean   | false   |
| list    | 图片列表，数组元素为图片的 URL 地址                          | string[]  | []      |
| index   | 初始显示的图片索引                                           | number    | 0       |


```
