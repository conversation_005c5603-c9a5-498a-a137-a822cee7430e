<script lang="ts" setup>
import { computed, ref, reactive } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { transferCustom, transferOrder } from "/@/api/order";
import { useAppStoreHook } from "/@/store/modules/app";
import { useUserStoreHook } from "/@/store/modules/user";

interface Props {
  value: boolean;
  transferType: boolean;
  type: string;
  id: any;
  msg: any;
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "getList"): void;
  (e: "closePage"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

function handleClose() {
  isModel.value = false;
}

let loading = ref<Boolean>(false);
let device = useAppStoreHook().device;

const ruleFormRef = ref<FormInstance>();
const form = reactive({
  orderid: [],
  note: "",
  workerId: undefined
});
const rules = reactive<FormRules>({
  workerId: [
    {
      required: true,
      message: "请选择坐席",
      trigger: "change"
    }
  ],
  note: [
    {
      required: true,
      message: "请填写转线索原因",
      trigger: "blur"
    }
  ]
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl || loading.value) return;
  await formEl.validate(valid => {
    if (valid) {
      loading.value = true;
      let comMath = props.type === "转单" ? transferOrder : transferCustom;
      let params = {
        ...form,
        infoUuids: form.orderid
      };
      comMath(params)
        .then(({ data }: { data: any }) => {
          loading.value = false;
          if (data.error) {
            ElMessage.info(`转移成功${data.success}条，失败${data.error}条`);
          } else {
            ElMessage.success("操作成功");
          }
          handleClose();
          //转单和批量转线索都是弹窗，此时关闭需要更新列表页面
          if (props.type === "转单" || props.transferType) {
            emit("getList");
          } else {
            emit("closePage");
          }
        })
        .catch(() => {
          loading.value = false;
        });
    } else {
      return false;
    }
  });
};

if (!props.transferType) {
  form.orderid = ref(props.type === "转线索" ? [props.id] : props.id);
} else {
  let ids = [];
  props.id.forEach(item => {
    ids.push(item.infoUuid);
  });
  form.orderid = ids;
}

let agentList = ref(
  useUserStoreHook().jobAgentList.map(item => {
    let obj: any = { ...item };
    obj.disabled =
      item.id === props.msg?.workerid ||
      (props.transferType &&
        props.id.findIndex(ele => obj.id === ele.workerid) > -1);
    return obj;
  })
);
</script>

<template>
  <el-dialog
    :title="props.transferType ? '批量' + props.type : props.type"
    v-model="isModel"
    :before-close="handleClose"
    append-to-body
  >
    <el-form
      :model="form"
      label-suffix="："
      :label-width="device !== 'mobile' ? '140px' : ''"
      ref="ruleFormRef"
      :class="{ mobile: device === 'mobile' }"
      :rules="rules"
      v-loading="loading"
    >
      <el-row>
        <el-col :lg="4" />
        <el-col :lg="16">
          <el-form-item label="当前归属坐席" v-if="!props.transferType">
            <el-input
              :value="useUserStoreHook().allAgentObj[props.msg.workerid]?.name"
              disabled
            />
          </el-form-item>
          <el-form-item prop="workerId" label="转给">
            <el-select-v2
              v-model="form.workerId"
              filterable
              clearable
              :options="agentList"
              placeholder="请选择坐席"
            />
          </el-form-item>
          <el-form-item label="转移原因" prop="note">
            <el-input
              clearable
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 6 }"
              placeholder="请填写转移原因"
              v-model="form.note"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="4" />
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="submitForm(ruleFormRef)">
        确定
      </el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>
