import { http } from "../utils/http";
import baseURL from "./url";

//一键外呼
export const call = data => {
  return http.request("post", `${baseURL.api}/web/call`, {
    data
  });
};

//挂断
export const hangup = () => {
  return http.request("get", `${baseURL.api}/web/hangup`);
};

//查询呼叫状态
export const callState = () => {
  return http.request("get", `${baseURL.api}/web/call/state`);
};

//顾客详情
export const detail = params => {
  return http.request("get", `${baseURL.api}/web/customer/detail`, {
    params
  });
};

//查询知识点
export const knowledge = params => {
  return http.request("get", `${baseURL.api}/web/knowledge/detail`, {
    params
  });
};

interface RegionInfo {
  province: Province;
  city: Province;
  district: Province;
}

interface Province {
  code: string;
  name: string;
  level: number;
  state: string;
}

//客户所在地
export const region = params => {
  return http.request<RegionInfo>("get", `${baseURL.api}/web/user/region`, {
    params
  });
};

//查询视频信息
export const video = params => {
  return http.request("get", `${baseURL.api}/web/video/detail`, {
    params
  });
};

//小学思维扩展课ab组
export const thinking = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-datapool/info/ab/primary_school`,
    {
      params
    }
  );
};

//查询用户授权
export const clearLimit = params => {
  return http.request(
    "delete",
    `${baseURL.api}/wuhan-datapool/info/address/cache/limit`,
    {
      params
    }
  );
};

//查询用户授权
export const auth = params => {
  return http.request("get", `${baseURL.api}/web/customer/auth/list`, {
    params
  });
};

//查询订单记录;
export const order = params => {
  return http.request<any[]>(
    "get",
    `${baseURL.api}/web/customer/raw/order/list`,
    {
      params
    }
  );
};

//查询工单列表
export const getWorkOrderApi = (userId: string) => {
  return http.request<{
    id: number;
    createdAt: string;
    ticketNumber: string;
    classification: string;
    status: string;
    acceptedBy: string;
  }>("get", `${baseURL.api}/sync-order/tickets/${userId}`);
};

//查询工单详情
export const getWorkOrderInfoApi = (id: number) => {
  return http.request<{ details: string }>(
    "get",
    `${baseURL.api}/sync-order/ticket/${id}`
  );
};

//查询用户最近观看记录
export const videoHistory = params => {
  return http.request(
    "get",
    `${baseURL.api}/web/customer/video/history/` + params
  );
};

//跟单记录
export const documentaryRecord = data => {
  return http.request("post", `${baseURL.api}/web/customer/call/record/list`, {
    data
  });
};

//跟单记录-添加
export const documentaryAdd = data => {
  return http.request("post", `${baseURL.api}/web/customer/call/record/add`, {
    data
  });
};

//赠送体验
export const giveVip = data => {
  return http.request("post", `${baseURL.api}/web/customer/give/vip`, {
    data
  });
};

//释放，并设置冷静期
export const release = data => {
  return http.request("post", `${baseURL.api}/web/customer/release`, {
    data
  });
};

//查询用户信息
export const findUser = data => {
  return http.request<{
    id: string;
    onionId: string;
    phone: string;
  }>("post", `${baseURL.api}/web/user/query`, {
    data
  });
};

//补差价查询V4
export const priceDiffFind = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/diffprice/query/v4`,
    {
      data
    }
  );
};

//平板续购
export const repurchaseFind = params => {
  return http.request("get", `${baseURL.api}/web/repurchase/strategy/list`, {
    params
  });
};

//补差价查询-新
export const priceDiffFindNew = data => {
  return http.request("post", `${baseURL.api}/wuhan-datapool/diffprice/query`, {
    data
  });
};

//获取补差价商品二维码
export const erCode = data => {
  return http.request(
    "post",
    `${baseURL.api}/web/diffprice/qrcode`,
    {
      data
    },
    {
      responseType: "arraybuffer"
    }
  );
};

//续购二维码
export const erCodeRepurchase = data => {
  return http.request(
    "post",
    `${baseURL.api}/web/repurchase/link`,
    {
      data
    },
    {
      responseType: "arraybuffer"
    }
  );
};

// 商品列表
export const shopList = data => {
  return http.request("post", `${baseURL.api}/web/h5/product/list`, {
    data
  });
};

//查询服务评价
export const evaluateFind = data => {
  return http.request("post", `${baseURL.api}/web/message/query`, {
    data
  });
};

//发送服务评价
export const evaluateSend = data => {
  return http.request("post", `${baseURL.api}/web/message/send`, {
    data
  });
};

//查询邀约短信模板列表
export const getMsgList = () => {
  return http.request("get", `${baseURL.api}/web/sms/template/list`);
};

//查询短信
export const msgFind = data => {
  return http.request("post", `${baseURL.api}/web/sms/before/call/query`, {
    data
  });
};

//发送短信
export const msgSend = data => {
  return http.request("post", `${baseURL.api}/web/sms/before/call/send`, {
    data
  });
};

//专项课列表查询
export const specialCourse = () => {
  return http.request("get", `${baseURL.api}/web/specialcourse/list`);
};

//修改销售机会状态
export const setRecommendStatus = data => {
  return http.request("post", `${baseURL.api}/web/recommend/status`, {
    data
  });
};

// 获取活动配置生效时间
export const getDemTime = () => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-worker/settings?name=activityPage`
  );
};

// 获取服务器时间
export const getServerTime = () => {
  return http.request("get", `${baseURL.api}/web/server/info`);
};

//推送支付订单
export const pushPay = data => {
  return http.request(
    "post",
    `${baseURL.api}/web/order/push/${data.pushType}`,
    {
      data
    }
  );
};

//推送支付订单
export const pushPayRecord = params => {
  return http.request(
    "get",
    `${baseURL.api}/sync-order/sync-order/pushOrder/list`,
    {
      params
    }
  );
};

//获取线索额外信息
export const getOtherMsg = params => {
  return http.request("get", `${baseURL.api}/web/customer/extra`, { params });
};

//保存线索额外信息
export const setOtherMsg = data => {
  return http.request("post", `${baseURL.api}/web/customer/extra`, {
    data
  });
};

//获取转介绍推荐人信息
export const getReferralMsg = params => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-datapool/info/referral_user`,
    { params }
  );
};

//获取企业微信抽奖推荐人信息
export const getLotteryDrawApi = id => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-miniprogram/lotteryDraw/relationship/takeByUserId/${id}`
  );
};
