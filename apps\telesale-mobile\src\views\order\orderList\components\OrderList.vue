<script lang="ts" setup>
import { storeToRefs } from "pinia";
import { useUserStore } from "@/store/modules/user";
import { getOrderListApi } from "@/api/order/index";
import { isDownloadApi } from "@/api/customer/details";
import { useSearch } from "@/hooks/useSearch";
import { useRouter } from "vue-router";
import MyList from "@/components/MyList/index.vue";
import { getLabel } from "@telesale/shared";
import {
  channelList,
  syncStatusList
} from "@telesale/shared/src/data/orderList";
import { closeToast, showLoadingToast, showToast } from "vant";
import { downloadApi } from "@/api/customer/done";
import { getPlatformListApi } from "@telesale/server/src/api/active/transfer";

const props = withDefaults(
  defineProps<{
    immediate?: boolean;
  }>(),
  {
    immediate: true
  }
);
const { allAgentObj, userMsg } = storeToRefs(useUserStore());
const router = useRouter();

const show = ref<boolean>(false);
const imgUrl = ref<string>("");
const tabList = ref();
const active = ref();
const rowData = ref();
const promotionId = ref();

const { list, loading, finished, error, onLoad, onSearch, searchForm } =
  useSearch({
    api: getOrderListApi,
    immediate: props.immediate
  });

const goDetail = (infoUuid: string, phone: string) => {
  router.push({
    path: "/customer/details",
    query: {
      infoUuid,
      phone
    }
  });
};

const getTabs = () => {
  getPlatformListApi().then(res => {
    tabList.value = res.data.list.map((item: any) => {
      return {
        label: item.name,
        name: item.id
      };
    });

    active.value = tabList.value?.[0]?.name;
  });
};

const open = row => {
  rowData.value = row;
  download();
};

function download() {
  showLoadingToast({});
  isDownloadApi({ userId: rowData.value.userid })
    .then(data => {
      if (data.quality) {
        promotionId.value = data.promotionId;
        getPoster();
      } else {
        showToast("该客户不符合转介绍条件");
      }
    })
    .catch(() => {
      closeToast();
      show.value = false;
    });
}

const getPoster = () => {
  showLoadingToast({
    zIndex: 100000
  });
  downloadApi({
    promotionId: promotionId.value,
    workerId: rowData.value.workerid,
    userId: rowData.value.userid,
    platformId: active.value
  })
    .then(res => {
      show.value = true;
      imgUrl.value = res.url;
    })
    .finally(() => {
      closeToast();
    });
};

getTabs();

defineExpose({
  onSearch,
  searchForm
});
</script>

<template>
  <div class="h-100%">
    <MyList
      v-model:error="error"
      :list="list"
      :loading="loading"
      :finished="finished"
      :onLoad="onLoad"
    >
      <template #default="{ data }">
        <div class="customer py-4">
          <div class="mb-20px">订单号 {{ data.orderid }}</div>
          <div class="mb-20px">手机号 {{ data.phone }}</div>
          <div class="flex flex-wrap gap-20px">
            <div class="tag" v-if="userMsg.leafNode">
              {{ allAgentObj?.[data.workerid]?.name }}
            </div>
            <div class="tag">
              {{ getLabel(data.syncType, channelList, "name", "id") }}
            </div>
            <div class="tag">
              {{ data.status }}
            </div>
            <div class="tag">
              同步状态
              {{ getLabel(data.syncStatus, syncStatusList, "name", "id") }}
            </div>
          </div>
          <div class="flex">
            <div
              class="qrcode-btn mr-20px"
              v-if="data.infoUuid"
              @click="goDetail(data.infoUuid, data.phone)"
            >
              查看详情
            </div>
            <div
              class="qrcode-btn mr-20px"
              v-auth="'telesale_admin_downloadPoster'"
              @click="open(data)"
              v-if="data.isPromotion"
            >
              下载海报
            </div>
          </div>
        </div>
      </template>
    </MyList>
    <van-dialog
      v-model:show="show"
      title="长按图片保存到相册"
      confirmButtonText="关闭"
    >
      <van-tabs v-model:active="active" class="mt-20px" @change="getPoster">
        <van-tab
          v-for="(item, index) in tabList"
          :key="index"
          :name="item.name"
          :title="item.label"
        >
          <div class="w-100% max-h-60vh overflow-y-auto text-center">
            <img class="w-100% h-100%" :src="imgUrl" />
          </div>
        </van-tab>
      </van-tabs>
    </van-dialog>
  </div>
</template>

<style lang="scss" scoped>
.customer {
  border-bottom: 1px solid #b4b4b4;
  .tag {
    padding: 4px 10px;
    background-color: #fff9e9;
    color: #a2653a;
    font-size: 24px;
  }
  .qrcode-btn {
    color: #3370ff;
    font-size: 24px;
    margin-top: 20px;
  }
}
</style>
