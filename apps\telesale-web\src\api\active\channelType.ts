/*
 * @Date         : 2024-03-05 18:39:27
 * @Description  : 渠道活码类型apis
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";
import baseURL from "./../url";

export interface ChannelType {
  id?: number;
  name: string;
  mutexGroups: number[];
}

/**
 * @description: 获取活码类型列表
 * @param {PageInfo} params
 * @returns { list: ChannelType[]; total: number; }
 */
export const getGroupListApi = () => {
  return http.request<{
    list: ChannelType[];
    total: number;
  }>("get", `${baseURL.api}/wuhan-miniprogram/qrcode_group/list`);
};

/**
 * @description: 获取活码类型详情
 * @param {PageInfo} params
 * @returns {ChannelType}
 */
export const getGroupInfoApi = (params: { id: number }) => {
  return http.request<ChannelType>(
    "get",
    `${baseURL.api}/wuhan-miniprogram/qrcode_group`,
    {
      params
    }
  );
};

/**
 * @description: 新增活码类型
 * @param {ChannelType} data
 */
export const addGroupApi = (data: ChannelType) => {
  return http.request("post", `${baseURL.api}/wuhan-miniprogram/qrcode_group`, {
    data
  });
};

/**
 * @description: 编辑活码类型列表
 * @param {ChannelType} data
 */
export const updateGroupApi = (data: ChannelType) => {
  return http.request("put", `${baseURL.api}/wuhan-miniprogram/qrcode_group`, {
    data
  });
};
