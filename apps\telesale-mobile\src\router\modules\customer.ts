import { RouteRecordRaw } from "vue-router";

const customerRouter: RouteRecordRaw[] = [
  {
    path: "/",
    name: "customerHome",
    component: () => import("@/views/customer/index.vue"),
    meta: {
      title: "客户管理",
      keepAlive: true
    }
  },
  {
    path: "/customer",
    component: () => import("@/views/customer/index.vue"),
    meta: {
      title: "客户管理",
      keepAlive: true,
      removeKeepAlive: ["customerDetail", "poolSearch"]
    }
  },
  {
    path: "/customer/pool",
    name: "customer",
    component: () => import("@/views/customer/pool/index.vue"),
    meta: {
      title: "客户池",
      removeKeepAlive: ["customerDetail", "poolSearch"]
    }
  },
  {
    path: "/customer/pool/search",
    name: "poolSearch",
    component: () => import("@/views/customer/pool/pages/searchCustomer.vue"),
    meta: {
      title: "客户池",
      keepAlive: true,
      removeKeepAlive: ["customerDetail"]
    }
  },
  {
    path: "/customer/done",
    name: "done",
    component: () => import("@/views/customer/done/index.vue"),
    meta: {
      title: "已成交",
      removeKeepAlive: ["customerDetail", "doneSearch"]
    }
  },
  {
    path: "/customer/done/search",
    name: "doneSearch",
    component: () => import("@/views/customer/done/pages/searchCustomer.vue"),
    meta: {
      title: "已成交",
      keepAlive: true,
      removeKeepAlive: ["customerDetail"]
    }
  },
  {
    path: "/customer/pool/manualInput",
    name: "poolInput",
    component: () => import("@/views/customer/pool/pages/manualInput.vue"),
    meta: {
      title: "人工录入",
      removeKeepAlive: ["customer"]
    }
  },
  {
    path: "/customer/details",
    name: "customerDetail",
    component: () => import("@/views/customer/details/index.vue"),
    meta: {
      title: "客户详情-",
      keepAlive: true
    }
  },
  {
    path: "/customer/give",
    component: () =>
      import("@/views/customer/details/pages/experienceGive.vue"),
    meta: {
      title: "体验赠送"
    }
  },
  {
    path: "/customer/pursuit",
    component: () => import("@/views/customer/details/pages/pursuit.vue"),
    meta: {
      title: "追单记录"
    }
  },
  {
    path: "/customer/cue",
    component: () => import("@/views/customer/details/pages/cue.vue"),
    meta: {
      title: "转线索"
    }
  },
  {
    path: "/customer/giveProfile",
    component: () => import("@/views/customer/details/pages/giveProfile.vue"),
    meta: {
      title: "赠送资料"
    }
  },
  {
    path: "/customer/release",
    component: () => import("@/views/customer/details/pages/release.vue"),
    meta: {
      title: "释放客户"
    }
  },
  {
    path: "/customer/payPush",
    component: () => import("@/views/customer/details/pages/payPush.vue"),
    meta: {
      title: "支付推送"
    }
  },
  {
    path: "/customer/addQrcode",
    component: () => import("@/views/customer/details/pages/addQrcode.vue"),
    meta: {
      title: "创建动态二维码"
    }
  },
  {
    path: "/customer/qrCode",
    component: () => import("@/views/customer/details/pages/qrcode.vue"),
    meta: {
      title: "动态二维码"
    }
  },
  {
    path: "/customer/exclusiveLink",
    component: () => import("@/views/customer/exclusiveLink/index.vue"),
    meta: {
      title: "专属链接"
    }
  },
  {
    path: "/customer/exclusiveLink/ipadLink",
    component: () => import("@/views/customer/exclusiveLink/pages/addIpad.vue"),
    meta: {
      title: "加购平板链接"
    }
  },
  {
    path: "/customer/exclusiveLink/qrCode",
    component: () => import("@/views/customer/exclusiveLink/pages/qrcode.vue"),
    meta: {
      title: "生成动态二维码"
    }
  },
  {
    path: "/customer/exclusiveLink/addLink",
    component: () => import("@/views/customer/exclusiveLink/pages/addLink.vue"),
    meta: {
      title: "创建会场链接"
    }
  },
  {
    path: "/customer/newExclusiveLink",
    component: () => import("@/views/customer/newExclusiveLink/index.vue"),
    meta: {
      title: "专属链接（新）"
    }
  },
  {
    path: "/customer/newExclusiveLink/qrCode",
    component: () =>
      import("@/views/customer/newExclusiveLink/pages/qrcode.vue"),
    meta: {
      title: "生成动态二维码"
    }
  },
  {
    path: "/customer/newExclusiveLink/addLink",
    component: () =>
      import("@/views/customer/newExclusiveLink/pages/addLink.vue"),
    meta: {
      title: "创建会场链接"
    }
  },
  {
    path: "/customer/newExclusiveLink/addNewLink",
    component: () =>
      import("@/views/customer/newExclusiveLink/pages/addNewLink.vue"),
    meta: {
      title: "创建会场链接（新）"
    }
  }
];

export default customerRouter;
