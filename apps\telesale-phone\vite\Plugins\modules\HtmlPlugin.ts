import HtmlPlugin, { Options } from 'vite-plugin-html-config'

export default function(plugins, env) {
  const HtmlPluginOptions: Options = {
    scripts: [
      {
        src: '//fp.yangcong345.com/web-track-1.1.4/webTrack.js',
        crossorigin: 'anonymous'
      },
      {
        src: '//fp.yangcong345.com/fe-monitor-1.2.20/monitor.js',
        crossorigin: 'anonymous'
      }
    ]
  }

  if (env === 'stage' || env === 'development' || env === 'test') {
    HtmlPluginOptions.headScripts = [
      {
        src: '//fp.yangcong345.com/middle/2.4.1/eruda.js'
      },
      {
        src: 'https://pagespy.yc345.tv/page-spy/index.min.js'
      },
      `
        eruda.init()
        eruda.position({ x: window.innerWidth - 50, y: window.innerHeight / 2 });
        let allDiv = document.documentElement.querySelectorAll('div')
        let erudaDom = allDiv ? allDiv[allDiv.length - 1] : null
        let erudaDevTools = erudaDom ? erudaDom.shadowRoot.querySelector('.eruda-dev-tools') : null
        erudaDevTools ? erudaDevTools.style['paddingBottom'] = 'calc(env(safe-area-inset-bottom) + 20px)' : null
      `,
      `
      window.$pageSpy = new PageSpy({
        project: 'telesale',
        title: 'telesale-phone'
      });
    `
    ]
  }

  plugins.push(HtmlPlugin(HtmlPluginOptions))
}
