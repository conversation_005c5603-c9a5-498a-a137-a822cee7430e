<!--
 * @Date         : 2024-06-07 14:50:42
 * @Description  : 赠送资料入口
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import GiveProfile from "../components/GiveProfile.vue";
import GiveProfileList from "../components/GiveProfileList.vue";

const active = ref<number>(0);
</script>

<template>
  <div>
    <van-tabs v-model:active="active">
      <van-tab title="赠送资料">
        <GiveProfile />
      </van-tab>
      <van-tab title="赠送记录">
        <GiveProfileList />
      </van-tab>
    </van-tabs>
  </div>
</template>

<style lang="scss" scoped></style>
