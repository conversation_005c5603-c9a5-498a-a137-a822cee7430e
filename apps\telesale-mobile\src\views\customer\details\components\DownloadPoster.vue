<!--
 * @Author: xia<PERSON>hen <EMAIL>
 * @Date: 2024-11-27 16:34:00
 * @LastEditors: xiaozhen <EMAIL>
 * @LastEditTime: 2025-05-20 15:38:31
 * @FilePath: /telesale-web_v2/apps/telesale-mobile/src/views/customer/details/components/DownloadPoster.vue
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
-->
<script lang="ts" setup>
import { ref } from "vue";
import { useUserStore } from "@/store/modules/user";
import { downloadFile } from "@telesale/shared";
import { storeToRefs } from "pinia";
import { downloadApi } from "@/api/customer/done";
import { closeToast, showLoadingToast } from "vant";
import { getPlatformListApi } from "@telesale/server/src/api/active/transfer";

interface Props {
  dataMemory: any;
  type?: string;
}

const props = withDefaults(defineProps<Props>(), {
  type: "customDetails"
});

const { userMsg } = storeToRefs(useUserStore());

const picUrl = ref("");

const tabList = ref();
const active = ref();

const getTabs = () => {
  getPlatformListApi().then(res => {
    tabList.value = res.data.list.map((item: any) => {
      return {
        label: item.name,
        name: item.id
      };
    });

    if (props.dataMemory?.picMsg?.promotionId > 0) {
      active.value = tabList.value?.[0]?.name;
      downloadMath(true);
    }
  });
};

function downloadMath(val = false) {
  showLoadingToast({});
  let params = {
    promotionId: props.dataMemory?.picMsg?.promotionId,
    workerId:
      props.type === "transferPoster"
        ? userMsg.value.id
        : props.dataMemory.workerid,
    userId: props.dataMemory.userid,
    platformId: active.value
  };
  downloadApi(params)
    .then(data => {
      if (val) {
        picUrl.value = data.url;
      } else {
        downloadFile(data.url, true);
      }
    })
    .finally(() => {
      closeToast();
    });
}

getTabs();
</script>

<template>
  <div class="d-box">
    <van-sticky :offset-top="44">
      <div class="bg-#eaf5ff text-40px py-20px">长按图片保存到相册</div>
    </van-sticky>
    <template v-if="props.dataMemory?.picMsg?.promotionId > 0">
      <van-tabs
        v-model:active="active"
        class="mt-20px"
        @change="downloadMath(true)"
      >
        <van-tab
          v-for="(item, index) in tabList"
          :key="index"
          :name="item.name"
          :title="item.label"
        >
          <img class="w-100% h-100%" :src="picUrl" alt="海报" />
        </van-tab>
      </van-tabs>
    </template>
    <div v-else style="margin: 50px 0">暂无上架的转介绍活动哦！</div>
  </div>
</template>
<style scoped lang="scss">
.d-box {
  text-align: center;
}
</style>
