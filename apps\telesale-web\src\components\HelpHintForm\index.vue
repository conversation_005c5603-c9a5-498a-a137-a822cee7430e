<script setup lang="ts">
interface Props {
  label: string;
  content: string;
  noColon?: Boolean; //没有冒号：的时候为true
}

const props = defineProps<Props>();
</script>

<template>
  <span>
    {{ props.label }}
    <el-popover placement="top" width="250" trigger="hover">
      <span v-html="content" />
      <template #reference>
        <IconifyIconOffline
          icon="question"
          style="
            cursor: pointer;
            color: #ccc;
            margin: 0 2px;
            vertical-align: text-top;
          "
        />
      </template>
    </el-popover>
    <template v-if="!noColon">：</template>
  </span>
</template>
