/*
 * @Date         : 2024-07-23 12:08:52
 * @Description  : 工号池api
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";
import baseURL from "../url";

export interface AgentNoReq {
  channelId: number;
  agentNo: number;
}

/**
 * @description: 获取待分配工号池
 * @param {AgentNoReq} params
 */
export const getAgentNoPollApi = (params: AgentNoReq) => {
  return http.request<{
    list: {
      id: number;
      channelId: number;
      agentNo: string;
      operatorId: number;
      operatorName: string;
      operatorAt: number;
    }[];
    total: number;
  }>("get", `${baseURL.api}/wuhan-callphone/agent_free_pool/list`, {
    params
  });
};

/**
 * @description: 删除待分配工号池
 * @param {number} id
 */
export const deleteAgentNoPollApi = (params: { id: number }) => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-callphone/agent_free_pool/delete`,
    {
      params
    }
  );
};

/**
 * @description: 解析并分析工号信息
 * @param {FormData} data
 */
export const parseAgentNoPoolApi = (data: FormData) => {
  return http.request<{
    list: {
      channel: {
        id: number;
        en: string;
        cn: string;
      };
      agentNo: string;
      errMsg: string;
    }[];
    total: number;
  }>(
    "post",
    `${baseURL.api}/wuhan-callphone/agent_free_pool/parse`,
    {
      data
    },
    {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

interface CreateAgentNo {
  agentFreePoolList: {
    channelId: number;
    agentNo: string;
    errMsg: string;
  };
}

/**
 * @description: 批量创建接口
 * @param {CreateAgentNo} data
 */
export const batchCreateAgentNoApi = (data: CreateAgentNo) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-callphone/agent_free_pool/batch_create`,
    {
      data
    }
  );
};

/**
 * @description: 导出工号
 * @param { AgentNoReq } data
 */
export const exportAgentNoApi = (params: CreateAgentNo) => {
  return http.request(
    "get",
    `${baseURL.api}/wuhan-callphone/agent_free_pool/export`,
    {
      params
    },
    {
      responseType: "arraybuffer"
    }
  );
};
