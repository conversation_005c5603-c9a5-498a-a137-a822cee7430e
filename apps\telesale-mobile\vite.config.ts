import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import vuejsx from "@vitejs/plugin-vue-jsx";
import { resolve } from "path";

import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { VantResolver } from "unplugin-vue-components/resolvers";

import viteCompression from "vite-plugin-compression";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";

import legacy from "@vitejs/plugin-legacy";

import UnoCss from "unocss/vite";
import vueSetupExtend from "vite-plugin-vue-setup-extend";
import OnionOssVitePlugin from "@guanghe-pub/onion-oss-vite-plugin";

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");

  console.log(mode);
  const config = {
    plugins: [
      legacy(),
      vue(),
      vuejsx(),
      vueSetupExtend(), // 配置在setup script标签上添加name 以在keepalive中使用include
      createSvgIconsPlugin({
        // 指定需要缓存的图标文件夹
        iconDirs: [resolve(process.cwd(), "src/assets/svg")],
        // 指定symbolId格式
        symbolId: "icon-[name]"
      }),
      AutoImport({
        resolvers: [VantResolver()],
        imports: ["vue"],
        dts: resolve(__dirname, "../../auto-imports.d.ts"),
        eslintrc: {
          enabled: true,
          filepath: resolve(__dirname, "../../.eslintrc-auto-import.json")
        }
      }),
      Components({
        resolvers: [VantResolver()]
      }),
      viteCompression(),
      UnoCss()
    ],
    resolve: {
      alias: {
        "@": resolve(__dirname, "src")
      }
    },
    base: "/WH_CRM_v2/telesale-mobile",
    root: ".",
    server: {
      cors: true, // 允许跨域
      port: 5176,
      strictPort: true,
      host: true
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import "src/scss/index.scss";`
        }
      }
    },
    build: {
      outDir: resolve(process.cwd(), "dist")
    }
  };

  if (env.VITE_ENV !== "development") {
    config.plugins.push(
      OnionOssVitePlugin({
        output: resolve(__dirname, "dist"),
        rootDir: "dist"
      })
    );
  }

  return config;
});
