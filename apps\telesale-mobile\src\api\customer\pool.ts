import {
  CustomerRes,
  ManualInputForm,
  SearchForm
} from "@/types/customer/pool";
import { http } from "@/utils/http";

// 客户池列表
export const getPoolListApi = (data: SearchForm): Promise<CustomerRes> => {
  return http.request<CustomerRes>("post", `/web/customer/list`, {
    data
  });
};

// 人工录入
export const manualAddApi = (data: ManualInputForm) => {
  return http.request("post", `/web/admin/customer/add`, {
    data
  });
};
