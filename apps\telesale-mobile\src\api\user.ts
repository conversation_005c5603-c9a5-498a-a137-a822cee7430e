import { UserInfo, agentType } from "@/types/user";
import { http } from "@/utils/http";

// 获取token
export const getTokenApi = (data: {
  code: string;
}): Promise<{
  token: string;
}> => {
  return http.request("post", `/wuhan-worker/token/noSecretLogin`, { data });
};

// 列出所有坐席
export const getAllAgentApi = (
  params?
): Promise<{ list: any; total: number }> => {
  return http.request("get", `/web/organization/worker/all`, {
    params
  });
};

// 获取登陆人信息
export const getUserInfoApi = (): Promise<UserInfo> => {
  return http.request("get", `/wuhan-worker/token/me`);
};

// 列出指定坐席所管理的坐席
export const findAgentApi = (): Promise<{
  list: agentType;
  total: number;
}> => {
  return http.request("get", `/web/organization/worker/manage`);
};

//获取当前坐席信息
export const getPersonMsgApi = () => {
  return http.request("get", `/web/profile/load`);
};

/**
 * @description: 是否是分期支付实验组坐席
 */
export const getIsInstallmentApi = (workerId: number) => {
  return http.request<{
    isInstallment: boolean;
  }>("post", `/wuhan-worker/worker/isInstallment`, {
    params: { workerId: workerId }
  });
};

/**
 * @description: 获取当前坐席下的组织架构
 */
export const findPositionApi = data => {
  return http.request<{
    pathList: any[];
  }>("post", `/web/organization/worker/query`, {
    data,
    isData: true
  });
};
