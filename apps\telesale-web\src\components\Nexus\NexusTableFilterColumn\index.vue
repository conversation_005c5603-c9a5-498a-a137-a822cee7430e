<!--
 * @Date         : 2023-11-14 15:56:11
 * @Description  : 可筛选列
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-table-column :label="label">
    <template #default="scope">
      <!-- 使用插槽内容，如果有的话 -->
      <slot v-if="$slots.default" :row="scope.row" />
      <!-- 否则使用默认渲染逻辑 -->
      <span v-else :class="[getOptionByVal(getValByProp(scope.row))?.class]">
        {{ getTextByVal(getValByProp(scope.row), scope.row) }}
      </span>
    </template>

    <template #header>
      <div class="flex justify-start items-center">
        <el-dropdown>
          <span class="flexD cursor-pointer c-[--el-color-info]">
            {{ label }}-{{ handleText }}
            <el-icon class="ml-5px"><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <div v-if="useSearch" class="p-5px">
              <el-input v-model="searchVal" placeholder="请输入筛选" />
            </div>

            <el-dropdown-menu>
              <el-dropdown-item @click="checkHandleState('all', '全部')">
                全部
              </el-dropdown-item>
              <el-dropdown-item
                v-for="(item, index) in dataList"
                :key="index"
                @click="checkHandleState(item.val, item.text)"
              >
                {{ item.text }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>
  </el-table-column>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import {
  ElInput,
  ElIcon,
  ElTableColumn,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem
} from "element-plus";
import { ArrowDown } from "@element-plus/icons-vue";

import "element-plus/es/components/input/style/css";
import "element-plus/es/components/icon/style/css";
import "element-plus/es/components/table-column/style/css";
import "element-plus/es/components/dropdown/style/css";
import "element-plus/es/components/dropdown-menu/style/css";
import "element-plus/es/components/dropdown-item/style/css";

const props = withDefaults(
  defineProps<{
    formatter?: any;
    tableRef: any;
    label: string;
    prop: string;
    paramKey?: string; // 避免查询字段与prop不一致 表格查询以此字段为主
    optionList: {
      text: string;
      val: any;
      showKey?: any; // 避免显示字段与key字段不一致 显示以此字段为主
      class?: string; // 要给该值添加的class
    }[];
    cb?: Function;
    useSearch?: boolean; // 是否开启搜索
  }>(),
  {
    formatter: () => {},
    useSearch: false
  }
);

function getTextByVal(val, row) {
  // if (typeof val === 'string') return val

  const target = props.optionList.find(
    item => (item.showKey ?? item.val) === val
  );
  if (props.formatter) {
    if (props.formatter(row)) return "";
  }

  return target?.text;
}

function getOptionByVal(val) {
  return props.optionList.find(item => (item.showKey ?? item.val) === val);
}

/**
 * @description: 根据prop获取值 支持a.b.c
 * @param {any} row 行数据
 * @return {*}
 */
function getValByProp(row: any) {
  if (!row) return undefined; // 如果行数据本身为空，直接返回undefined
  if (props.prop.includes(".")) {
    return props.prop.split(".").reduce((obj, property) => {
      // 在访问下一个属性前，检查当前对象是否为null或undefined
      return obj?.[property];
    }, row);
  } else {
    return row[props.prop];
  }
}

// 处理状态
const handleState = ref("all");
const handleText = ref("全部");

function checkHandleState(state: string, text: string) {
  if (handleState.value === state) return;

  handleState.value = state;
  handleText.value = text;
  props.tableRef.searchHandle({
    [props.paramKey ?? props.prop]:
      handleState.value === "all" ? null : handleState.value
  });

  if (props.cb)
    props.cb({ handleState: handleState.value, handleText: handleText.value });
}

function checkAll() {
  checkHandleState("all", "全部");
}

const searchVal = ref("");
const dataList = computed(() => {
  if (searchVal.value) {
    return props.optionList.filter(item => item.text.includes(searchVal.value));
  }
  return props.optionList;
});

defineExpose({
  handleState,
  handleText,
  checkAll
});
</script>

<style scoped lang="scss"></style>
