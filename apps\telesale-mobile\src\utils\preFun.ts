import utils from '@constq/qzr-utils'
import { platform, getQueryObject } from '@guanghe-pub/onion-utils'
// import { useRole } from '@/store/role'

/**
 * 自定信息方法
 */
export function preFun() {
  // useRole().getRole()

  setSafeCss()

  // const userInfo = new utils.monitor.UserInfo()
  // console.log('用户信息', userInfo)

  // const performance = new utils.monitor.Performance()
  // console.log('性能指标', performance)

  // const errorPerformance = new utils.monitor.ErrorPerformance()
  // console.log('错误监控', errorPerformance)
}


export function setSafeCss() {
  let safeBottom = '0px'
  let safeBottomNoDefault

  let safeTop = '0px'
  let safeTopNoDefault

  let notchLeftWidth = '0px'
  let notchLeftWidthNoDefault
  if (platform.isIOS()) {
    if (platform.isUpperVersionIOS('11.2')) {
      // env(safe-area-inset-top) iOS 11.2之后才生效
      safeTop = 'env(safe-area-inset-top)'
      safeTopNoDefault = 'env(safe-area-inset-top, null)'
      notchLeftWidth = 'env(safe-area-inset-left)'
      notchLeftWidthNoDefault = 'env(safe-area-inset-left, null)'
      safeBottom = 'env(safe-area-inset-bottom)'
      safeBottomNoDefault = 'env(safe-area-inset-bottom, null)'
    } else {
      // iOS11之前没有刘海屏手机，直接固定状态栏高度
      safeTop = '20px'
      safeBottom = 'constant(safe-area-inset-bottom)'
    }

    // 临时处理IPAD 后期优化 PS：目前 isUpperVersionIOS 版本判断有问题
    let currentVersion = navigator.userAgent.match(/OS (\d+)_(\d+)_?(\d+)?/) || []
    if (!currentVersion.length) {
      safeBottom = 'env(safe-area-inset-bottom)'
    }
  } else {
    let bottomHeight = getQueryObject().tabBarHeight
    if (bottomHeight && Number(bottomHeight) > 0) {
      safeBottom = ''.concat(bottomHeight, 'px')
      safeBottomNoDefault = ''.concat(bottomHeight, 'px')
    }
  }
  document.documentElement.style.setProperty('--safe-area-bottom', safeBottom)
  document.documentElement.style.setProperty('--safe-area-bottom-no-default', safeBottomNoDefault)
  document.documentElement.style.setProperty('--safe-area-top', safeTop)
  document.documentElement.style.setProperty('--safe-area-top-no-default', safeTopNoDefault)

}
