<template>
  <el-date-picker
    v-model="datePicker"
    :type="type"
    range-separator="至"
    :default-value="defaultValue"
    :disabled-date="disabledDateHandle()"
    :default-time="defaultTime"
    @calendar-change="changeDate"
    v-bind="$attrs"
  />
</template>

<script lang="ts" setup>
import { computed, watch } from "vue";
import { useVModel } from "@vueuse/core";
import useDatePicker from "/@/hooks/useDatePicker";

const props = withDefaults(
  defineProps<{
    value: any;
    isDisableDate?: boolean;
    limitTime?: boolean;
    inputType?: string;
    initDate?: Function;
  }>(),
  {
    isDisableDate: true
  }
);

const emit = defineEmits<{
  (e: "update:value", value: any): void;
}>();

const valueDate = useVModel(props, "value", emit);

const type = computed<string>(() => {
  if (props.inputType) return props.inputType;
  return "daterange";
});

const {
  datePicker,
  disabledDate,
  changeDate,
  defaultTime,
  defaultValue,
  initDatePickToDay,
  unixmsDatePicker,
  unixsDatePicker
} = useDatePicker();

function disabledDateHandle() {
  if (!props.isDisableDate) {
    return () => {
      return false;
    };
  }

  if (props.limitTime) {
    return disabledDate;
  } else {
    return time => {
      const timeUnix = time.getTime();
      const outTimePick = timeUnix > new Date().getTime();

      return outTimePick;
    };
  }
}

watch(
  () => datePicker,
  n => {
    valueDate.value = n;
  },
  {
    immediate: true,
    deep: true
  }
);

defineExpose({
  datePicker,
  initDatePickToDay,
  unixmsDatePicker,
  unixsDatePicker
});
</script>

<style scoped lang="scss"></style>
