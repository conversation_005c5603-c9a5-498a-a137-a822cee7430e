<script lang="ts" setup>
import { ref } from "vue";
import { GiveForm } from "@/types/customer/action";
import { giveVip<PERSON><PERSON>, giveBigVip<PERSON><PERSON> } from "@/api/customer/action";
import { closeToast, showLoadingToast, showToast } from "vant";
import { useRoute, useRouter } from "vue-router";
import MySelect from "@/components/MySelect/index.vue";
import MyMultiple from "@/components/MyMultiple/index.vue";
import { subjectList } from "@telesale/shared/src/data/customer";

const route = useRoute();
const router = useRouter();

const dayList: LabelValueOption[] = [
  { label: "1天", value: 1 },
  { label: "2天", value: 2 },
  { label: "3天", value: 3 },
  { label: "4天", value: 4 },
  { label: "5天", value: 5 },
  { label: "6天", value: 6 },
  { label: "7天", value: 7 }
];

const dayData = computed(() =>
  form.value.type === "1" ? dayList.filter(item => item.value < 4) : dayList
);

const form = ref<GiveForm>({
  type: "1",
  targetIdList: [],
  time: undefined,
  userid: route.query.userId as string
});

const changeType = () => {
  form.value.targetIdList = [];
  form.value.time = undefined;
};

const onSubmit = () => {
  if (form.value.targetIdList.length > 2) {
    return showToast("最多只能赠送2个科目");
  }
  const data = form.value;
  data.start = Math.floor(+new Date() / 1000);
  data.end = (form.value.time as number) * 24 * 60 * 60 + data.start;
  const params = {
    userId: form.value.userid,
    addDay: form.value.time
  };
  const fn = form.value.type === "1" ? giveVipApi : giveBigVipApi;
  const fnParans: any = form.value.type === "1" ? data : params;

  showLoadingToast({});
  fn(fnParans).then(() => {
    showToast("赠送成功");
    setTimeout(() => {
      router.go(-1);
    }, 1500);
  });
};
</script>

<template>
  <div>
    <van-form required class="mt-20px" @submit="onSubmit">
      <van-cell-group inset>
        <van-field
          label="赠送类型"
          name="type"
          required
          :rules="[
            {
              required: true,
              message: '请选择赠送类型',
              trigger: 'onChange'
            }
          ]"
        >
          <template #input>
            <van-radio-group
              v-model="form.type"
              class="flex gap-20px"
              @change="changeType"
            >
              <van-radio :icon-size="16" name="1">体验课</van-radio>
              <van-radio :icon-size="16" name="2"> 体验版大会员 </van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <MyMultiple
          v-if="form.type === '1'"
          v-model:value="form.targetIdList"
          label="科目"
          placeholder="请选择科目"
          :columns="subjectList"
          :options="{
            label: 'label',
            value: 'target'
          }"
          :multipleLimit="2"
          :rules="[
            { required: true, message: '请选择科目', trigger: 'onChange' }
          ]"
        />
        <MySelect
          v-model:value="form.time"
          label="赠送天数"
          placeholder="请选择赠送天数"
          :columns="dayData"
          :rules="[
            { required: true, message: '请选择赠送天数', trigger: 'onChange' }
          ]"
        />
      </van-cell-group>
      <div style="margin: 16px">
        <van-button round block type="primary" native-type="submit">
          提交
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<style lang="scss" scoped></style>
