1、@用户名的时候，发送预览需要飞书的openid，否则发送预览会报错

2、文字链接的链接地址，必须为一个完整的地址，例如：https://www.baidu.com ,否则发送预览会报错

3、发送预览时同时会复制解析后飞书可以的json代码，可直接粘贴到飞书模板设置的json模块

4、此卡片预览，主要是移动端的设置，后面加的双列文本选型，主要为PC端的使用

5、如果是为PC端假的模板内容，卡片预览效果与实际效果会有些出入，实际效果以点击发送预览到PC端飞书查看的为主

6、双列文本的填充类型是不支持分割线的

7、双列文本某一项的填充内容和内容中的填充内容，最少保留一个内容，用来占位

8、双列文本在同一个模块内，最少有两项，如果没有，直接使用更为合适的内容选型卡

9、双列文本的新增列按钮，意思是在同一个模块内，继续追加一项，根据所在列宽设置，自动换行，此方式换行后上下内容间隔距离较小


