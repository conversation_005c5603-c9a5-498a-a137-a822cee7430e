import compareMax from "/@/utils/handle/compareMax";

function mergeArray(list) {
  const result = [];
  list.forEach(item => {
    const { courseId } = item;
    if (!result[courseId]) {
      result[courseId] = {
        courseId,
        list: []
      };
    }
    result[courseId].list.push({ ...item });
  });
  const rtnResult = [];
  Object.values(result).forEach(item => {
    item.list.sort(compareMax("realDeductiblePrice"));
    item.list.forEach((ele, index) => {
      ele.totalAmount = item.list.reduce((prev, curr) => {
        return prev + curr.deductiblePrice;
      }, 0);
      ele.totalDays = item.list.reduce((prev, curr) => {
        return prev + curr.realDeductibleDay;
      }, 0);
      ele.totalRealAmount = item.list.reduce((prev, curr) => {
        return prev + curr.realDeductiblePrice;
      }, 0);
      ele.deductMonth = Math.floor(ele.totalDays / 31);
      ele.daysNotInMonth = ele.totalDays % 31;
      ele.totalDaysName =
        ele.deductMonth + "个月" + ele.daysNotInMonth + "天" + ele.courseName;
      ele.totalRealAmountName = "￥" + ele.totalRealAmount.toFixed(2);
      ele.span = index ? 0 : item.list.length;
      ele.totalAmountName = "￥" + ele.totalAmount.toFixed(2);
      ele.realDeductiblePriceName = "￥" + ele.realDeductiblePrice.toFixed(2);
      ele.deductiblePriceName = "￥" + ele.deductiblePrice.toFixed(2);
      ele.totalAmountName = "￥" + ele.totalAmount.toFixed(2);
      ele.realDeductibleDayName = ele.realDeductibleDay + "天";
      ele.powerName =
        Math.floor(ele.realDeductibleDay / 31) +
        "个月" +
        (ele.realDeductibleDay % 31) +
        "天" +
        ele.courseName;
      ele.span = index ? 0 : item.list.length;
      ele.realDeductiblePriceHtml =
        ele.deductiblePrice > ele.realDeductiblePrice
          ? `<span style='color:#F56C6C'>${ele.realDeductiblePriceName}</span>`
          : ele.realDeductiblePriceName;
      rtnResult.push(ele);
    });
  });
  return rtnResult;
}

export default mergeArray;
