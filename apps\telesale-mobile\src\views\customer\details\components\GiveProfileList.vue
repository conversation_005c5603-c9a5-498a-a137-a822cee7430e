<!--
 * @Date         : 2024-06-14 11:51:43
 * @Description  : 赠送资料历史
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import { useUserStore } from "@/store/modules/user";
import { storeToRefs } from "pinia";
import { showLoadingToast, closeToast } from "vant";
import { useRoute } from "vue-router";
import { giveProfileRecordApi } from "@/api/customer/action";

const list = ref<any[]>([]);
const route = useRoute();
const { allAgentObj } = storeToRefs(useUserStore());

const getList = () => {
  const { userId } = route.query;
  if (!userId) return;
  showLoadingToast({});
  giveProfileRecordApi({ userId: userId as string })
    .then(res => {
      list.value = res.sendRecord;
    })
    .catch(err => {
      list.value = [];
    })
    .finally(() => {
      closeToast();
    });
};

getList();
</script>

<template>
  <div class="container-order">
    <div v-if="list.length > 0" class="order-list">
      <div v-for="(item, index) in list" :key="index" class="order-item mb-4">
        <van-cell-group :border="false">
          <van-cell
            :border="false"
            title="资料名称"
            :value="item.materialName"
          />
          <van-cell :border="false" title="赠送时间" :value="item.giftTime" />
          <van-cell
            :border="false"
            title="赠送人"
            :value="allAgentObj?.[item.workerId]?.name"
          />
        </van-cell-group>
      </div>
    </div>
    <div v-else>
      <van-empty description="暂无数据" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.container-order {
  padding: 20px;
  box-sizing: border-box;
  :deep(.van-cell__title) {
    flex: none;
    width: 200px;
  }
  :deep(.van-cell__value) {
    width: calc(100% - 200px);
  }
}
</style>
