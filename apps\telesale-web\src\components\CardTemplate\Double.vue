<script setup lang="ts" name="Double">
import { computed } from "vue";

interface Props {
  initData: any; //初始化数据
}
interface Emits {
  (e: "update:initData", val: any): void;
  (e: "update:divIndex", val: number): void;
  (e: "update:spanIndex", val: number): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const cardPreviewItem = computed({
  get() {
    return props.initData;
  },
  set(val: any) {
    emit("update:initData", val);
  }
});

function classHandler(item) {
  return {
    bold: item.styles.indexOf("bold") > -1,
    italics: item.styles.indexOf("italics") > -1,
    deleteLine: item.styles.indexOf("deleteLine") > -1,
    blueColor: item.type === "link" || item.type.indexOf("at") > -1,
    hr: item.type === "hr"
  };
}
function markdownClick(e) {
  if (e.target.dataset.id === "span") {
    emit("update:spanIndex", Number(e.target.dataset.index));
    emit("update:divIndex", Number(e.target.parentElement.dataset.index));
  }
}
</script>
<template>
  <div class="c-markdown" @dblclick="markdownClick($event)">
    <div
      data-id="div"
      v-for="(ele, i) in cardPreviewItem.json.actions"
      :key="i"
      :data-index="i"
      class="c-col"
      :class="{
        isLong: !ele.is_short
      }"
    >
      <span
        v-for="(item, index) in ele.activeList"
        :key="index"
        :class="classHandler(item)"
        data-id="span"
        :data-index="index"
      >
        <div v-if="item.styles.indexOf('changeLine') > -1" />
        {{ item.text }}
      </span>
    </div>
  </div>
</template>
<style scoped lang="scss">
.c-markdown {
  display: flex;
  flex-wrap: wrap;
  .c-col {
    flex: 0 0 50%;
    word-break: break-word;
    &.isLong {
      flex: 0 0 100%;
    }
    > span {
      line-height: 1.6;
      cursor: pointer;

      &:last-child.hr {
        margin-bottom: 0;
      }
    }
  }

  .bold {
    font-weight: bold;
  }

  .italics {
    font-style: italic;
  }

  .deleteLine {
    text-decoration: line-through;
  }

  .blueColor {
    color: #245bdb;
  }

  .hr {
    display: block;
    margin-bottom: 16px;
    height: 17px;
    background-color: #fff;
    border-bottom: 1px solid #1f232926;
  }
}
</style>
