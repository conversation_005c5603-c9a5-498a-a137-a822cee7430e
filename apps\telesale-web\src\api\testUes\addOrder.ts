/*
 * @Date         : 2024-07-31 18:18:34
 * @Description  : 新建订单
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";
import baseURL from "../url";

export interface AddUserReq {
  phone: string;
  type: string;
  registEntranceId: string;
  os: string;
  role: string;
  realIdentity: string;
}

// 创建用户
export const addOnionUserApi = (data: AddUserReq) => {
  return http.request<{
    id: string;
    name: string;
    phone: string;
    email?: any;
    nickname: string;
    channel: string;
    type: string;
    registEntranceId: string;
    os: string;
    role: string;
    countryCode: string;
    onionId: string;
    regionCode: string;
    realIdentity: string;
    thirdPartyOauths?: any;
    salt: string;
    password: string;
    gender: string;
    schoolYear: string;
    school_id: number;
    attribution: string;
    value: string;
  }>("post", `${baseURL.api}/web/usercore/user`, {
    data
  });
};

interface OrderReq {
  userId: string;
  goodId: string;
  status: string;
  paymentCredentials: PaymentCredentials;
  creationWay: CreationWay;
  paidTime: string;
  refundedTime: string;
  updatedBy: string;
  extra: Extra;
}

interface Extra {
  relevance: string;
}

interface CreationWay {
  bySelf: string;
  productId: string;
}

interface PaymentCredentials {
  id: string;
  amount: number;
  source: string;
}

// 创建订单
export const createOrderApi = (data: Partial<OrderReq>) => {
  return http.request("put", `${baseURL.api}/web/order/manualRecord`, {
    data
  });
};
