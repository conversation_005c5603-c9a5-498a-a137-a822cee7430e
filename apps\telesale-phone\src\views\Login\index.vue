<!--
 * @Date         : 2024-07-16 17:01:17
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div v-loading="loading"
       class="d-login-container">
    <div class="d-login-box">

      <div class="mb-20px">
        <div class="d-login-title mb-15px">飞书邮箱登陆</div>

        <div class="flexD">
          <el-text size="small"
                   class="text-18px!"
                   type="warning">（验证码将通过短信方式发送至您绑定的手机号中）</el-text>
        </div>

      </div>
      <el-form ref="ruleFormRef"
               :model="form"
               :rules="rules">
        <el-form-item prop="mail">
          <el-input v-model.trim="form.mail"
                    placeholder="请输入您的飞书邮箱"
                    clearable
                    @keyup.enter="submitForm(ruleFormRef)" />
        </el-form-item>
        <el-form-item ref="codeItemRef"
                      prop="code">
          <el-input v-model.trim="form.code"
                    type="number"
                    placeholder="请输入验证码"
                    clearable
                    @keyup.enter="submitForm(ruleFormRef)">
            <template #append>
              <el-button :class="isDisabled ? 'd-login-disabled' : 'd-login-send'"
                         type="primary"
                         :disabled="isDisabled"
                         @click="sendCode">
                {{ codeMsg }}
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-button :disabled="!form.mail || !form.code || loading"
                   class="d-login-btn"
                   type="primary"
                   @click="submitForm(ruleFormRef)">
          登 录
        </el-button>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { useRouter } from 'vue-router'
import api from '@/api'
import { useTimeout } from './hooks/useTimeout'
import { useUser } from '@/store/user'
import router from '@/router'

// const router = useRouter()
const userStore = useUser()

const { start, stop, count } = useTimeout()

const loading = ref(false)
const phoneRule = /^1[3-9]\d{9}$/
const codeRule = /^\d{6}$/
// 邮箱正则表达式
const emailRule = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/

const codeMsg = ref<string>('获取验证码')
const ruleFormRef = ref<FormInstance>()
const form = reactive({
  mail: '',
  code: ''
})
const rules = reactive<FormRules>({
  mail: [
    {
      required: true,
      message: '请输入飞书邮箱',
      trigger: 'blur'
    },
    {
      message: '邮箱格式有误',
      pattern: emailRule,
      trigger: 'blur'
    }
  ],
  code: [
    {
      required: true,
      message: '请输入验证码',
      trigger: 'blur'
    },
    {
      message: '验证码格式有误',
      pattern: codeRule,
      trigger: 'blur'
    }
  ]
})

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl || loading.value || !form.mail || !form.code) return
  await formEl.validate(async valid => {
    if (valid) {
      loading.value = true
      try {
        await userStore.login({ ...form })

      } catch (error) {
        console.log(error)
      }

      loading.value = false
    } else {
      return false
    }
  })
}

let isDisabled = computed(() => {
  console.log('count', count.value)
  if (!form.mail || count.value > 0) {
    return true
  }

  return !emailRule.test(form.mail)
})

let countDown = () => {
  if (count.value > 60) {
    codeMsg.value = '获取验证码'
    stop()
    count.value = 0
  } else {
    codeMsg.value = 60 - count.value + 's'
    start(countDown, 1000)
  }
}

const sendCode = () => {
  api.verifyCode({ mail: form.mail }).then(() => {
    start(countDown, 1000)
    ElMessage.success('验证码发送成功')
  })
}
</script>

<style scoped lang="scss">
.d-login-container {
  // background-color: #75a0e1;
  background-color: white;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.d-login-box {
  box-sizing: border-box;
  flex: 0 0 300PX;
  height: 340PX;
  padding: 50PX 30PX;
  // box-shadow: 2PX 2PX 2PX rgba(0, 21, 41, 0.1);
  background-color: #fff;

  .d-login-title {
    text-align: center;
    font-size: 20PX;
    font-weight: bold;
  }

  :deep(.el-form-item) {
    margin-bottom: 35PX;

    //number类型隐藏上下箭头
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      -webkit-appearance: none;
    }

    //number类型光标上移
    .el-input__inner {
      line-height: 1PX !important;
    }

    .el-input {
      width: 100%;
      height: 35PX;
    }

    .el-input__wrapper {
      border-radius: 0;
    }

    .el-form-item__error {
      margin-top: 4PX;
    }

    .el-input-group__append {
      box-shadow: none;

      button {
        border-radius: 0;
        font-size: 14PX;
      }

      .d-login-send {
        border-color: #409eff;
        background-color: #409eff;
        color: #fff;

        height: 35PX;

        &:hover {
          color: var(--el-button-hover-text-color);
          border-color: var(--el-button-hover-border-color);
          background-color: var(--el-button-hover-bg-color);
          outline: 0;
        }
      }

      .d-login-disabled {
        height: 35PX;
        min-width: 80PX;
        border: none;
        color: var(--el-button-disabled-text-color);
        background-color: var(--el-button-disabled-bg-color);
        border-color: var(--el-button-disabled-border-color);
      }
    }
  }

  .d-login-btn {
    width: 100%;
    height: 35PX;
    margin-top: 10PX;
    font-size: 16PX;
  }
}
</style>
