/*
 * @Date         : 2025-01-24 15:41:47
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";

export interface DiffPriceReq {
  userid?: string;
}

export interface DiffPriceInfo {
  strategyId?: string;
  strategyName?: string;
  deductibleOrders?: {
    id: string;
    name: string;
    deductAmount: number;
  }[];
  reasonDetail?: string[];
  deductAmount?: number;
  amount?: number;
}
[];

export interface DiffPriceInfoRes {
  list?: DiffPriceInfo[];
}

/**
 * @description 根据用户id查询补差价详情-2025年1月
 * https://yapi.yc345.tv/project/2352/interface/api/120498
 * <AUTHOR>
 * @date 2025-01-22
 * @export
 * @param {DiffPriceReq} data
 * @returns {Promise<DiffPriceInfoRes>}
 */
export const getNewDiffPriceListApi = (data: DiffPriceReq) => {
  return http.request<DiffPriceInfoRes>(
    `post`,
    `/wuhan-datapool/diffprice/query/v6`,
    {
      data,
      isData: true
    }
  );
};
