<script lang="ts" setup>
import { ref } from "vue";
import {
  differenceQrcodeApi,
  DiifPriceParams,
  repurchaseQrcodeApi
} from "@/api/customer/details";
import { closeToast, showLoadingToast, showToast } from "vant";
import { useRoute, useRouter } from "vue-router";
import SaveImageDialog from "@/components/SaveImageDialog/index.vue";
import {
  isStagesList,
  stagesType
} from "@telesale/shared/src/data/exclusiveLink";
import { useUserStore } from "@/store/modules/user";
import { storeToRefs } from "pinia";
import { getInstallmentPayType } from "@telesale/shared/src/businessHooks/payPush/installmentPay";
import { getArrayBufferBase64 } from "@/utils/common";

const { query, meta } = useRoute();

const router = useRouter();
const titleObj = {
  repurchase: "续购",
  diffFind: "补差价"
};

document.title = `${titleObj[query.type as string]}-${meta.title}`;

// const { qrcodeList, qrcodeType, qrcodeValue } = useQrcode(getAuth);

const form = ref<DiifPriceParams>({
  from: "telesale",
  name: "",
  isInstallment: 2,
  installmentPayType: ["alipayFq"]
});

const imgUrl = ref<string>("");
const show = ref<boolean>(false);
const { isStages } = storeToRefs(useUserStore());

const changeInstallment = () => {
  form.value.installmentPayType = ["alipayFq"];
};

const onSubmit = () => {
  showLoadingToast({});
  let fn: null | Function = null;
  let params: any = {};
  if (query.type === "repurchase") {
    params = {
      type: query.repurchaseType,
      from: form.value.from,
      orderId: query.paidOrderId,
      name: query.name
    };
    fn = repurchaseQrcodeApi;
  } else {
    params = {
      uri: query.uri,
      from: form.value.from,
      strategyType: "",
      schoolYear: "",
      dynamic: true,
      name: query.cloneName,
      strategyId: query?.strategyId,
      courseName: query.cloneName
    };
    fn = differenceQrcodeApi;
  }
  // qrcodeType.value = form.value.from;
  params.installmentPayType = getInstallmentPayType(
    form.value.isInstallment as number,
    form.value.installmentPayType
  );
  Reflect.deleteProperty(params, "isInstallment");
  fn(params).then(async (res: ArrayBuffer) => {
    const url = await getArrayBufferBase64(res);
    imgUrl.value = url;
    showToast("操作成功");
    show.value = true;
  });
};
</script>

<template>
  <div>
    <van-form required class="mt-20px" @submit="onSubmit">
      <van-cell-group inset>
        <van-field readonly label="商品名称">
          <template #input>
            {{ query.type === "repurchase" ? query.name : query.goodName }}
          </template>
        </van-field>
        <!-- <MySelect
          v-model:value="form.from"
          label="项目组"
          placeholder="请选择项目组"
          :columns="qrcodeList"
          :rules="[
            { required: true, message: '请选择项目组', trigger: 'onChange' }
          ]"
        /> -->
        <template v-if="isStages">
          <van-field
            name="radio"
            label="分期支付"
            required
            :rules="[
              {
                required: true,
                message: '请选择分期支付',
                trigger: 'onChange'
              }
            ]"
          >
            <template #input>
              <van-radio-group
                v-model="form.isInstallment"
                direction="horizontal"
                @change="changeInstallment"
              >
                <van-radio
                  v-for="(item, index) in isStagesList"
                  :key="index"
                  :name="item.value"
                >
                  {{ item.label }}
                </van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <van-field
            v-if="form.isInstallment === 1"
            name="checkboxGroup"
            label="分期支付方式"
            required
            :rules="[
              {
                required: true,
                message: '请选择分期支付方式',
                trigger: 'onChange'
              }
            ]"
          >
            <template #input>
              <van-checkbox-group
                v-model="form.installmentPayType"
                direction="horizontal"
              >
                <van-checkbox
                  v-for="(item, index) in stagesType"
                  :key="index"
                  :name="item.value"
                  shape="square"
                >
                  {{ item.label }}
                </van-checkbox>
              </van-checkbox-group>
            </template>
          </van-field>
        </template>
      </van-cell-group>
      <div style="margin: 16px" class="flex gap-20px">
        <van-button class="flex-1" type="primary" native-type="submit">
          生成二维码
        </van-button>
        <van-button class="flex-1" @click="router.back()">返回</van-button>
      </div>
    </van-form>
    <SaveImageDialog v-model:show="show" :imgUrl="imgUrl" />
  </div>
</template>

<style lang="scss" scoped></style>
