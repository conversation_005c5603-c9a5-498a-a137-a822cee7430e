<script setup lang="ts" name="But<PERSON>">
interface Props {
  initData: any; //初始化数据
}

const props = withDefaults(defineProps<Props>(), {});
</script>
<template>
  <div class="c-button" :class="props.initData.json.actions[0].activeColor">
    <span class="c-button-text">
      {{ props.initData.json.actions[0].activeText }}
    </span>
  </div>
</template>
<style scoped lang="scss">
.c-button {
  cursor: pointer;
  height: 32px;
  line-height: 32px;
  border-radius: 6px;
  border: 1px solid #d0d3d6;
  text-align: center;
  margin-top: 16px;
  padding: 0 36px;

  &:hover {
    background-color: #eff0f1;
  }

  .c-button-text {
    color: #1f2329;
    font-weight: 400;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    word-break: break-all;
    display: -webkit-box !important;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &.primary {
    border-color: #3370ff;

    &:hover {
      background-color: #e1eaff;
    }

    .c-button-text {
      color: #3370ff;
    }
  }

  &.danger {
    border-color: #f54a45;

    &:hover {
      background-color: #fde2e2;
    }

    .c-button-text {
      color: #f54a45;
    }
  }
}
</style>
