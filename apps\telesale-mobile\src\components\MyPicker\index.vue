<template>
  <van-popup :show="show" round position="bottom">
    <van-picker
      v-model="selectedValues"
      :columns="props.columns"
      :columns-field-names="{
        text: props.options.label,
        value: props.options.value
      }"
      @cancel="show = !show"
      @confirm="onConfirm"
    />
  </van-popup>
</template>

<script lang="ts" setup>
import { getLabel } from "@/utils/common";
import { computed } from "vue";
const emits = defineEmits(["update:value", "update:show", "confirm"]);

const props = withDefaults(
  defineProps<{
    value: any;
    columns: any[];
    show: boolean;
    options?: {
      label: string;
      value: string;
    };
  }>(),
  {
    options: () => {
      return {
        label: "label",
        value: "value"
      };
    }
  }
);

const text = ref<string>("");
const selectedValues = ref<string[] | number[]>([]);
const showValue = computed({
  get() {
    return props.value;
  },
  set(val) {
    emits("update:value", val);
  }
});

const show = computed({
  get() {
    return props.show;
  },
  set(val: boolean) {
    emits("update:show", val);
  }
});

const onConfirm = ({ selectedOptions }) => {
  if (!selectedOptions[0]) {
    show.value = false;
    return;
  }
  const { value, label } = props.options;
  text.value = selectedOptions[0][label];
  showValue.value = selectedOptions[0][value];
  show.value = !show.value;
  emits("confirm", { label: text.value, value: showValue.value });
};

watch(
  () => showValue.value,
  n => {
    if (!n) {
      text.value = "";
      selectedValues.value = [];
    } else {
      init();
    }
  }
);

const init = () => {
  text.value = getLabel(
    props.value,
    props.columns,
    props.options.label,
    props.options.value
  );
  selectedValues.value = [showValue.value];
};

init();
</script>
