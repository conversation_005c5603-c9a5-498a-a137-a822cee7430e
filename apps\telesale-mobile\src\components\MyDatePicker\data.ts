export const getYearList = () => {
  const year = new Date().getFullYear();
  const start = year - 5;
  const end = year + 5;
  const list: any = [];
  for (let index = start; index <= end; index++) {
    list.push({
      text: index + "年",
      value: index
    });
  }
  return list;
};

export const getMonthList = () => {
  const list: any[] = [];
  for (let index = 1; index < 13; index++) {
    list.push({
      text: index + "月",
      value: index
    });
  }
  return list;
};

export const getDayList = (year: number, month: number) => {
  const day = new Date(year, month, 0).getDate();
  const list: any[] = [];
  for (let index = 1; index <= day; index++) {
    list.push({
      text: index + "日",
      value: index
    });
  }
  return list;
};

const getList = (number: number, unit: string) => {
  const list: any[] = [];
  for (let index = 0; index < number; index++) {
    list.push({
      text: (index > 9 ? index : "0" + index) + unit,
      value: index
    });
  }
  return list;
};

export const hList = getList(24, "时");
export const mList = getList(60, "分");
export const sList = getList(60, "秒");
