import { http } from "/@/utils/http";
import baseURL from "../url";
import {
  ConditionRes,
  CueModelForm,
  CueModelInfo,
  CueModelQuery
} from "/@/types/system/cueModel";

// 获取线索模型列表
export const getCueModelListApi = (params: CueModelQuery) => {
  return http.request<{
    list: CueModelInfo[];
    total: number;
  }>("get", `${baseURL.api}/wuhan-datapool/leads/model/list`, {
    params
  });
};

// 获取线索模型详情
export const getCueModelInfoApi = params => {
  return http.request<CueModelForm>(
    "get",
    `${baseURL.api}/wuhan-datapool/leads/model`,
    {
      params
    }
  );
};

// 新增线索模型
export const createCueModelApi = (data: CueModelForm) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/leads/model/create`,
    {
      data
    }
  );
};

// 编辑线索模型
export const updateCueModelApi = (data: CueModelForm) => {
  return http.request(
    "put",
    `${baseURL.api}/wuhan-datapool/leads/model/update`,
    {
      data
    }
  );
};

// 上线线索模型
export const releaseCueModelApi = (params: { id: number }) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/leads/model/release`,
    {
      params
    }
  );
};

// 取消上线线索模型
export const cancelReleaseCueModelApi = (data: { id: number }) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/leads/cancelReleaseLeadsModel`,
    {
      data
    }
  );
};

// 线索模型描述
export const descCueModelApi = (params: { stage: string }) => {
  return http.request<{
    description: string[];
  }>("get", `${baseURL.api}/wuhan-datapool/leads/model/group/desc`, { params });
};

// 获取条件组列表
export const getConditionListApi = () => {
  return http.request<ConditionRes>(
    "get",
    `${baseURL.api}/wuhan-datapool/leads/model/condition/list`
  );
};
