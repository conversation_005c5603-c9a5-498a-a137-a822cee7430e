/*
 * @Date         : 2025-02-13 18:26:37
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */

import { http } from "../../utils/http";
import baseURL from "../url";

export interface LinkSettingReq {
  visibleType?: number;
  invisibleType?: number;
  invisibleTime: any;
  invisibleGroups?: number[];
  invisibleStart?: number;
  invisibleEnd?: number;
  inGroups?: boolean;
}

/**
 * @description: 获取会场链接配置可见范围
 * @returns {ArrayBuffer}
 */
export const getLinkSettingApi = () => {
  return http.request<LinkSettingReq>(
    "get",
    `${baseURL.api}/wuhan-datapool/venue_link/setting`
  );
};

/**
 * @description: 设置会场链接配置可见范围
 * @param {IpadReq} data
 * @returns {ArrayBuffer}
 */
export const settingLinkApi = (data: LinkSettingReq) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/venue_link/setting`,
    {
      data
    }
  );
};

export interface DiffSettingData {
  action?: "add" | "update" | "delete";
  skuGoodId?: string;
  showQR?: boolean;
  visibleToPart?: boolean;
  visibleGroups?: number[];
  inGroups?: boolean;
}

/**
 * @description: 获取补差价链接配置可见范围列表
 */
export const getDiffSettingApi = () => {
  return http.request<{
    list: any[];
    infos: any[];
  }>("get", `${baseURL.api}/wuhan-datapool/difference_link/setting/list`);
};

/**
 * @description: 获取补差价链接配置可见范围详情
 */
export const getDiffSettingInfoApi = (params: { skuGoodId: string }) => {
  return http.request<DiffSettingData>(
    "get",
    `${baseURL.api}/wuhan-datapool/difference_link/setting/get`,
    { params }
  );
};

/**
 * @description: 补差价链接配置可见范围
 */
export const updateDiffSettingApi = (data: DiffSettingData) => {
  return http.request<{
    list: any[];
    infos: any[];
    total: number;
  }>("post", `${baseURL.api}/wuhan-datapool/difference_link/setting`, { data });
};

export interface VenueLinkGoodsReqQuery {
  /**
   * 配置ID
   */
  id?: number;
  /**
   * 商品ID
   */
  goodsId?: string;
  /**
   * 商品类型
   */
  showType?: number;
  /**
   * 是否有可见范围
   */
  inGroups?: boolean;
}

export interface VenueLinkGoods {
  /**
   * ID
   */
  id?: number;
  /**
   * 商品ID
   */
  goodsId?: string;
  /**
   * 商品名称
   */
  goodsName?: string;
  /**
   * 商品类型
   */
  showType: number;
  groupIds?: number[];

  /**
   * 是否有可见范围
   */
  inGroups?: boolean;
}

export interface VenueLinkGoodsResBody {
  /**
   * 商品列表
   */
  list: VenueLinkGoods[];
  /**
   * 总数
   */
  total: number;
}

/**
 * @description 创建会场链接商品配置
 * https://yapi.yc345.tv/mock/2352/wuhan-datapool/getVenueLinkGoods
 * <AUTHOR>
 * @date 2025-04-24
 * @export
 * @param {VenueLinkGoodsReqQuery} data
 * @returns {Promise<VenueLinkGoodsResBody>}
 */
export const addVenueLinkGoodsApi = (data: VenueLinkGoods) => {
  return http.request<VenueLinkGoodsResBody>(
    `post`,
    `${baseURL.api}/wuhan-datapool/createVenueLinkGoods`,
    {
      data
    }
  );
};

/**
 * @description 更新会场链接商品配置
 * https://yapi.yc345.tv/mock/2352/wuhan-datapool/getVenueLinkGoods
 * <AUTHOR>
 * @date 2025-04-24
 * @export
 * @param {VenueLinkGoodsReqQuery} data
 * @returns {Promise<VenueLinkGoodsResBody>}
 */
export const updateVenueLinkGoodsApi = (data: VenueLinkGoods) => {
  return http.request<VenueLinkGoodsResBody>(
    `put`,
    `${baseURL.api}/wuhan-datapool/updateVenueLinkGoods`,
    {
      data
    }
  );
};

/**
 * @description 获取商品详情
 * https://yapi.yc345.tv/mock/2352/wuhan-datapool/getVenueLinkGoodsDetail
 * <AUTHOR>
 * @date 2025-04-24
 * @export
 * @param {number} id peizhiID
 * @returns {Promise<VenueLinkGoodsResBody>}
 */
export const getVenueLinkGoodsDetailApi = (params: { id: number }) => {
  return http.request<VenueLinkGoods>(
    `get`,
    `${baseURL.api}/wuhan-datapool/getVenueLinkGoods`,
    {
      params
    }
  );
};

/**
 * @description 删除商品
 * https://yapi.yc345.tv/mock/2352/wuhan-datapool/deleteVenueLinkGoods
 * <AUTHOR>
 * @date 2025-04-24
 * @export
 * @param {number} id 商品ID
 */
export const deleteVenueLinkGoodsApi = (params: { id: number }) => {
  return http.request(
    `delete`,
    `${baseURL.api}/wuhan-datapool/deleteVenueLinkGoods`,
    {
      params
    }
  );
};

/**
 * @description 获取商品列表
 * https://yapi.yc345.tv/mock/2352/wuhan-datapool/getVenueLinkGoodsList
 * <AUTHOR>
 * @date 2025-04-24
 * @export
 * @returns {Promise<VenueLinkGoodsResBody>}
 */
export const getVenueLinkGoodsListApi = () => {
  return http.request<VenueLinkGoodsResBody>(
    `get`,
    `${baseURL.api}/wuhan-datapool/listVenueLinkGoods/list`
  );
};
