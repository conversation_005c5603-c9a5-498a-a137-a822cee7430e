/*
 * @Date         : 2024-05-20 17:26:51
 * @Description  : 客户池相关api
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */
import { http } from "../../utils/http";
import baseURL from "../url";

export interface BatchClueList {
  userId: string;
  msg?: string;
}

/**
 * @description: 上传批量释放文件
 * @param {FormData} data
 * @returns {BatchClueList}
 */
export const uploadClueApi = (data: FormData) => {
  return http.request<{
    batchReleaseInfos: BatchClueList[];
  }>(
    "post",
    `${baseURL.api}/wuhan-datapool/workerAllocateInfo/batch_release_upload`,
    {
      data
    },
    {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

/**
 * @description: 导入批量释放的数据
 * @param {BatchClueList} data
 * @returns {BatchClueList}
 */
export const importClueApi = (data: { batchReleaseInfos: BatchClueList[] }) => {
  return http.request<{
    batchReleaseInfos: BatchClueList[];
  }>("post", `${baseURL.api}/wuhan-datapool/workerAllocateInfo/batch_release`, {
    data
  });
};

export interface ClueInfo {
  infoUuid: string;
  location: string;
  onionId: string;
  phone: string;
  workerId: number;
  createdAt: string;
  userExpire: string;
}

/**
 * @description: 搜索批量释放的数据
 * @param {string} searchInfo
 * @returns {BatchClueList}
 */
export const searchClueApi = (params: { searchInfo: string }) => {
  return http.request<{
    infos: ClueInfo[];
  }>(
    "get",
    `${baseURL.api}/wuhan-datapool/workerAllocateInfo/findByPhoneOrOnionId`,
    {
      params
    }
  );
};
