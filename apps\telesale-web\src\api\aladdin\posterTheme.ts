/*
 * @Date         : 2024-03-27 16:29:46
 * @Description  : 海报主题相关apis
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";
import baseURL from "../url";

export type ThemeReq = {
  name: string;
  status: string;
};

export interface ThemeRes {
  id: number;
  createdAt: string;
  name: string;
  posterPath: string;
  operatorId: number;
  status: string;
}

/**
 * @description: 获取海报列表
 * @param {ThemeReq} params
 */
export const getThemeListApi = (params: ThemeReq & PageInfo) => {
  return http.request<{
    list: ThemeRes[];
    total: number;
  }>("get", `${baseURL.api}/web/alading/promotion`, {
    params
  });
};

export interface ThemeInfo {
  name: string;
  posterPath: string;
}

/**
 * @description: 获取主题详情
 * @param {object} params
 * @returns {ThemeInfo}
 */
export const getThemeInfoApi = (id: number) => {
  return http.request<ThemeInfo>(
    "get",
    `${baseURL.api}/web/alading/promotion/detail/${id}`
  );
};

/**
 * @description: 新增主题
 * @param {ThemeInfo} data
 */
export const addThemeApi = (data: ThemeInfo) => {
  return http.request("post", `${baseURL.api}/web/alading/promotion`, {
    data
  });
};

/**
 * @description: 编辑主题详情
 * @param {ThemeInfo} data
 */
export const updateThemeApi = (data: ThemeInfo) => {
  return http.request("put", `${baseURL.api}/web/alading/promotion`, {
    data
  });
};

/**
 * @description: 更新主题状态
 * @param {id,status} data
 */
export const updateThemeStatusApi = (data: { id: number; status: string }) => {
  return http.request("put", `${baseURL.api}/web/alading/promotion/status`, {
    data
  });
};
