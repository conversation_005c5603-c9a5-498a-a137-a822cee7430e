import { http } from "../../utils/http";
import baseURL from "../url";

interface KnowledgeSet {
  id: number;
  tipSetId: number;
  score: string;
  knowledgeID: string;
}

interface TipSet {
  id: number;
  workerId: number;
  name: string;
  score: string;
  knowledgeSets: KnowledgeSet[];
}

export function getTipSet(params): Promise<
  ReturnValue<{
    tipSets: TipSet[];
  }>
> {
  return http.get(`${baseURL.robot}/admin/tipSet`, { params });
}

export function updateTipSet(params: { tipSets: TipSet[]; workerId: number }) {
  return http.post(`${baseURL.robot}/admin/tipSet`, {
    data: params
  });
}
