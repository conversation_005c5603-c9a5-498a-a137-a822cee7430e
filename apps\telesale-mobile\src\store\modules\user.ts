import {
  findAgent<PERSON><PERSON>,
  getAll<PERSON>gent<PERSON>pi,
  getIsInstallmentApi,
  getPersonMsgApi,
  getUserInfoApi
} from "@/api/user";
import { UserInfo, agentType } from "@/types/user";
import { defineStore } from "pinia";
interface UserState {
  userInfo: UserInfo | null;
  // 所有坐席列表-不包含离职人员(前缀job)
  jobAllAgentList: agentType[];
  // 所有坐席列表
  allAgentList: agentType[];
  // 所有作息对象
  allAgentObj: Record<number, agentType> | null;
  // 所有权限
  authorizationMap: string[];
  // 对应组织架构下的坐席列表-不包含离职人员(前缀job)
  jobAgentList: agentType[];
  // 对应组织架构下的坐席列表
  agentList: agentType[];
  // 当前登录人坐席信息
  userMsg: { leafNode?: boolean; id?: number; name?: string; avatar?: string };
  isStages: boolean;
}

export const useUserStore = defineStore("user", {
  state: (): UserState => {
    return {
      userInfo: null,
      userMsg: {},
      jobAllAgentList: [],
      allAgentList: [],
      allAgentObj: null,
      authorizationMap: [],
      jobAgentList: [],
      agentList: [],
      isStages: true
    };
  },
  actions: {
    setUserInfo(info: UserInfo) {
      this.userInfo = info;
    },
    async getAllAgentObj() {
      const res = await getAllAgentApi();
      const allAgentObj = res.list;
      const obj = {};
      const allAgentList: agentType[] = [];
      this.jobAllAgentList = allAgentObj.filter((item: agentType) => {
        item.status === 2 && (item.name += "（已离职）");
        item.label = item.name;
        item.value = item.id;
        obj[item.id] = item;
        allAgentList.push(item);
        return item.status !== 2;
      });
      this.allAgentList = allAgentList;
      this.allAgentObj = obj;
    },
    setAuthorizationMap(authorizationMap: string[]) {
      this.authorizationMap = [...new Set(authorizationMap)];
    },
    setAgentList(agentList) {
      this.jobAgentList = agentList.filter(item => {
        item.status === 2 && (item.name += "（已离职）");
        item.label = item.name;
        item.value = item.id;
        return item.status !== 2;
      });
      this.agentList = agentList;
    },
    getUserInfoAction() {
      getUserInfoApi().then(res => {
        this.setUserInfo(res);
        this.setAuthorizationMap(res.permissions);
      });
    },
    async getAgentList() {
      const res = await findAgentApi();
      this.setAgentList(res.list);
    },
    async getUserMsg() {
      getPersonMsgApi().then(async (res: any) => {
        res.leafNode = res.property === "manager";
        this.userMsg = res;
        try {
          const reslut = await getIsInstallmentApi(res.id);
          this.isStages = reslut.isInstallment;
        } catch (error) {
          this.isStages = false;
        }
      });
    }
  }
});
