---
description:
globs: *.vue
alwaysApply: false
---
---
name: vue-rules
description: Vue.js project best practices and coding standards
globs:
  - "src/**/*.vue"
  - "src/**/*.ts"
priority: 2
---

# Vue 项目规范

## 组件规范

### 基础规则
- 使用 Composition API
- 组件名使用 PascalCase
- Props 定义使用 defineProps
- 事件使用 defineEmits

### 目录结构

src/
├── components/        # 通用组件
├── views/            # 页面视图
├── utils/            # 工具函数
├── api/              # API 接口
├── types/            # TypeScript 类型
└── assets/           # 静态资源

### 组合式函数规范

- 以 `use` 开头命名
- 一个函数只做一件事
- 返回响应式数据
- 注意依赖收集

### 性能优化

- 合理使用 computed
- v-show vs v-if
- 使用 defineAsyncComponent
- keep-alive 缓存

### TypeScript 集成

- 使用 defineComponent
- Props 类型声明
- 组件实例类型
- 响应式类型

## 状态管理

- 使用 Pinia
- 模块化状态管理
- 合理使用 storeToRefs

## css生成规则

所有css优先使用unocss的方式进行实现

## 引入规则

如果项目内配置了unplugin-auto-import，那么可以自动引入vue、element-plus、nexus-components的api，无需引入

## nexus-components规则

在代码内用nexus-table替代el-table，用nexus-form替代el-form

## element-plus规则

在nexus-form或el-form组件内，将rules以内联的方式定义在el-form-item上
ElMessageBox使用await的方式调用

## 关于vue的双向绑定

优先使用defineModel实现，不要使用prop+emit+computed的方式
