<!--
 * @Date         : 2024-07-17 11:25:22
 * @Description  : 数字键盘
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div>
    <div class="w-100vw flexD mt-100px">
      <div class="w-650px flex justify-between items-center h-50px">
        <span v-if="callStore.checkCanInputPhone || callStore.isActive "
              class="text-80px c-black flexD flex-1 w-100%">
          <input v-model="phone"
                 maxlength="11"
                 class="w-100% border-none b-b-1px b-b-#999 b-b-solid text-center"
                 autofocus
                 @input="inputPhone">
        </span>
        <el-icon v-if="callStore.checkCanInputPhone"
                 color="#909399"
                 size="30px"
                 @click="phone=''"><CircleClose /></el-icon>
      </div>
    </div>

    <div v-if="!callStore.checkCanInputPhone"
         class="flex items-center flex-col justify-between h-50vh mt-100px">
      <div class="flexD flex-col">
        <el-text class="text-40px! mb-20px!">{{ CallStateStr[callStore.callState] }}</el-text>

        <el-text v-if="callStore.callState === CallState.CONFIRMED"
                 class="text-40px!">{{ callStore.callTimeStr }}</el-text>
      </div>

      <div class="relative">
        <div class="absolute right-180px">
          <el-button class="w-120px! h-120px!"
                     circle
                     @click="handleMute()">
            <el-icon v-if="!callStore.isMute"
                     :size="30"
                     color="#409EFF"><Microphone /></el-icon>
            <el-icon v-else
                     :size="30"
                     color="#F56C6C"><Mute /></el-icon>
          </el-button>
        </div>

        <el-button type="danger"
                   class="w-120px! h-120px!"
                   circle
                   @click="handleCall(phone);phone=''">
          <el-icon :size="30"><PhoneFilled /></el-icon>
        </el-button>

      </div>
    </div>

    <div v-if="callStore.checkCanInputPhone"
         class="keyboard-box">
      <van-number-keyboard v-model="phone"
                           :show="showBoard"
                           :hide-on-click-outside="false"
                           theme="custom"
                           :maxlength="11"
                           :extra-key="['*', '#']"
                           close-button-text="呼出"
                           @close="call" />
    </div>
  </div>
</template>

<script lang='ts' setup>
import { CircleClose, PhoneFilled, Mute, Microphone } from '@element-plus/icons-vue'
import { CallState, CallStateStr } from '../types/CallState'
import { useCall } from '@/store/call'
import useMute from '../hooks/useMute'

const callStore = useCall()
const { handleMute } = useMute()

const props = defineProps<{
  handleCall:(phone: string) => void
}>()

const showBoard = computed(() => {
  return callStore.state
})
const phone = ref('')

function call() {
  if (phone.value === '') {
    ElMessage.error('请输入需要外呼的手机号～')
    return
  }

  if (phone.value.length !== 11) {
    ElMessage.error('手机号长度有误，请重新输入～')
    return
  }

  const reg = /^1[3-9]\d{9}$/
  if (!reg.test(phone.value)) {
    ElMessage.error('手机号格式有误，请重新输入～')
    return
  }

  props.handleCall(phone.value)
}

function keyHandle(e) {
  e.stopPropagation()
  e.preventDefault()

  if (!callStore.state) return
  if (!callStore.checkCanInputPhone) return

  if (e.key === 'Backspace') {
    phone.value = phone.value.slice(0, -1)
  }

  if (e.key === 'Enter') {
    call()
  }

  if (phone.value.length >= 11) return

  const reg = /^[0-9]$/
  if (reg.test(e.key)) {
    phone.value = `${phone.value}${e.key}`
  }
}

// onMounted(() => {
//   nextTick(() => {
//     document.addEventListener('keydown', keyHandle)
//   })
// })

// onUnmounted(() => {
//   document.removeEventListener('keydown', keyHandle)
// })

function inputPhone() {
  console.log(phone.value)
  // 移除字符串中所有的空格和非数字
  phone.value = phone.value.replace(/\s+|\D/g, '')
}
</script>

<style scoped lang='scss'>
.keyboard-box {
  :deep(.van-key--blue) {
    background: #67C23A;
  }
}

</style>
