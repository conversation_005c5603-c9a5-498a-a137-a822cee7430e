<script lang="ts" setup name="customerDetail">
import { useRoute, useRouter } from "vue-router";
import {
  getCustomerInfoApi,
  getKnowledgeApi,
  getLockList<PERSON>pi,
  getRegionApi,
  getThinkingApi,
  isDownload<PERSON>pi,
  lock<PERSON>ue<PERSON><PERSON>,
  unlockCueApi,
  NewQue,
  getNewQueApi,
  getPhoneWhiteApi
} from "@/api/customer/details";
import { CustomerInfo } from "@/types/customer/details";
import Info from "./components/Info.vue";
import OrderList from "./components/OrderList.vue";
import VedioList from "./components/VedioList.vue";
import DocumentaryRecord from "./components/DocumentaryRecord.vue";
import PriceDifference from "./components/PriceDifference.vue";
import Renew from "./components/Renew.vue";
import { showConfirmDialog, showToast } from "vant";
import { getAuth } from "@/utils/common/auth";
import DownloadPoster from "./components/DownloadPoster.vue";
import dayjs from "dayjs";
import AddPad from "./components/AddPad.vue";
import Report from "./components/Report.vue";
import RenewTab from "./components/RenewTab.vue";
import DiffPrice from "./components/DiffPrice.vue";

const route = useRoute();
const router = useRouter();

const info = ref<CustomerInfo>();
const show = ref<boolean>(false);
const hasRisk = ref<boolean>(false);
const active = ref<number>(0);
const actions = ref([
  {
    name: "锁定",
    key: "lock",
    auth: "telesale_admin_custom_lock"
  },
  { name: "转线索", key: "cue", auth: "telesale_admin_custom_transfer" },
  {
    name: "释放",
    key: "release",
    color: "#ee0a24",
    auth: "telesale_admin_custom_release"
  },
  {
    name: "资料赠送",
    key: "giveProfile",
    auth: "telesale_admin_giveProfile"
  }
]);
const isLock = getAuth("telesale_admin_custom_lock");
actions.value = actions.value.filter(item => getAuth(item.auth));

const isNewQuestions = ref<boolean>(false);

const getInfo = () => {
  document.title =
    "客户详情-" + (route.query.phone || route.query.onionid || "");
  getCustomerInfoApi({ infoUuid: route.query.infoUuid as string }).then(res => {
    info.value = res;
    getThinking();
    getKnowledge();
    getRegion();
    isDownloadMath();
    getPhoneWhite();
    if (isLock) {
      getLockStatus();
    }
  });
};

const getDetail = () => {
  getCustomerInfoApi({ infoUuid: route.query.infoUuid as string }).then(res => {
    info.value!.role = res.role;
  });
};

const getPhoneWhite = () => {
  if (!info.value?.phone) {
    hasRisk.value = true;
    return;
  }
  getPhoneWhiteApi({ userId: info.value.userid }).then(res => {
    hasRisk.value = res.hasRisk;
  });
};

const isHave = ref(false);
const dataMemory = ref();
function isDownloadMath() {
  if (!info.value?.userid) return;
  isDownloadApi({ userId: info.value?.userid }).then(res => {
    if (res.quality) {
      dataMemory.value = {
        picMsg: res,
        userid: info.value?.userid,
        workerid: info.value?.workerid
      };
      isHave.value = true;
    }
  });
}

//查询客户所在地
const getRegion = () => {
  if (!info.value?.userid) return;
  getRegionApi({ userid: info.value.userid }).then(res => {
    info.value!.region =
      typeof res === "string"
        ? res
        : res.province.name + res.city.name + res.district.name;

    if (
      (res.province?.code || res.city?.code || res.district?.code) &&
      info.value?.stage !== "高中"
    ) {
      getIsNewQuestions(
        res.district?.code || res.city?.code || res.province?.code
      );
    }
  });
};

const getIsNewQuestions = (code: string) => {
  getNewQueApi({ regionCode: code })
    .then(res => {
      isNewQuestions.value = res.isNewExamArea;
    })
    .catch(() => {
      isNewQuestions.value = false;
    });
};

const getLockStatus = () => {
  getLockListApi({
    infoUuids: [info.value!.infoUuid]
  }).then(res => {
    actions.value.forEach(item => {
      if (item.key === "lock" || item.key === "unlock") {
        item.key = res?.length > 0 ? "unlock" : "lock";
        item.name = res.length > 0 ? "解锁" : "锁定";
      }
    });
  });
};

//查询看的哪个视频触发线索
const getThinking = () => {
  if (!info.value?.userid) return;
  getThinkingApi({ userId: info.value.userid }).then(res => {
    info.value!.hasThinking = res.isA || false;
  });
};

const getKnowledge = () => {
  const { topicId } = info.value || {};
  if (topicId) {
    getKnowledgeApi({ id: topicId }).then(res => {
      info.value!.knowledge = res.name;
    });
  }
};

const onSelect = item => {
  const { key } = item;

  if (key === "lock" || key === "unlock") {
    const statusStr = key === "lock" ? "锁定" : "解锁";
    showConfirmDialog({
      title: "提示",
      message: `确定${statusStr}此线索吗？`
    }).then(() => {
      const fn = key === "lock" ? lockCueApi : unlockCueApi;
      fn({
        infoUuid: info.value!.infoUuid,
        familyId: info.value!.familyId
      }).then(() => {
        showToast(statusStr + "成功");
        getLockStatus();
      });
    });
    return;
  }
  router.push({
    path: "/customer/" + key,
    query: {
      infoUuid: route.query.infoUuid,
      workerId: info.value?.workerid,
      userId: info.value?.userid
    }
  });
};

const sendClass = () => {
  if (hasRisk.value) {
    showToast("当前用户未绑定手机号或者手机号存在风险，不支持赠送体验课");
    return;
  }
  go("/customer/give");
};

const go = (path?: string) => {
  router.push({
    path,
    query: {
      userId: info.value?.userid,
      infoUuid: route.query.infoUuid,
      onionid: info.value?.onionid,
      stage: info.value?.stage,
      grade: info.value?.grade
    }
  });
};

onActivated(() => {
  getInfo();
});
</script>

<template>
  <div class="container">
    <van-notice-bar color="red" background="#ecf9ff" v-if="hasRisk">
      ❗️当前用户未绑定手机号或者手机号存在风险，不支持赠送体验课！风险校验不一定100%准确，若跟用户核实无风险，可联系团长解除风险标记
    </van-notice-bar>
    <div class="tabs">
      <van-tabs v-model:active="active" lazy-render sticky swipe-threshold="4">
        <van-tab title="基础信息" :name="0">
          <Info :data="info" :isNewQue="isNewQuestions" @success="getDetail" />
        </van-tab>
        <van-tab title="订单信息" :name="1">
          <OrderList :userId="info?.userid" />
        </van-tab>
        <van-tab title="观看记录" :name="2">
          <VedioList :userId="info?.userid" />
        </van-tab>
        <van-tab
          title="补差价查询（新）"
          name="diffPrice"
          v-if="getAuth('telesale_admin_diff_price')"
        >
          <DiffPrice :userId="info!.userid" />
        </van-tab>
        <van-tab
          title="续购"
          :name="6"
          v-if="getAuth('telesale_admin_repurchase')"
        >
          <RenewTab
            :userid="info!.userid"
            :key="info!.userid ? 1 : 2"
            :isShowOperation="true"
            type="repurchase"
          />
        </van-tab>

        <van-tab title="学情报告" :name="3" v-if="info?.userid">
          <div class="w-screen relative">
            <Report
              style="width: 100%; height: calc(100vh - 96px)"
              :userId="info?.userid"
            />
          </div>
        </van-tab>
        <van-tab title="跟单记录" :name="4">
          <DocumentaryRecord v-if="info?.infoUuid" :infoUuid="info.infoUuid" />
        </van-tab>
        <template v-if="isHave && getAuth('telesale_admin_downloadPoster')">
          <van-tab title="下载海报" :name="7">
            <DownloadPoster
              :dataMemory="dataMemory || {}"
              :key="dataMemory ? 1 : 2"
            />
          </van-tab>
        </template>
      </van-tabs>
    </div>
    <div class="action">
      <div
        class="action-item"
        v-if="info?.userid"
        @click="go('/customer/payPush')"
      >
        <van-icon name="bill-o" size="20" />
        支付推送
      </div>
      <div
        class="action-item"
        v-auth="'telesale_admin_custom_give'"
        @click="sendClass"
      >
        <van-icon name="gift-o" size="20" />
        体验赠送
      </div>
      <div
        v-auth="'telesale_admin_custom_documentary_add'"
        class="action-item"
        @click="go('/customer/pursuit')"
      >
        <van-icon name="edit" size="20" />
        追单记录
      </div>
      <div class="action-item" @click="show = true">
        <van-icon name="ellipsis" size="20" />
        更多
      </div>
    </div>
    <van-action-sheet
      v-model:show="show"
      :actions="actions"
      cancel-text="取消"
      close-on-click-action
      @select="onSelect"
    />
  </div>
</template>

<style lang="scss" scoped>
.container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  .tabs {
    position: relative;
    left: 0;
    top: 0;
    width: 100%;
    height: calc(100vh - 100px);
    overflow: hidden auto;
    :deep(.van-tabs__content) {
      min-height: calc(100vh - 185px);
    }
  }
  .action {
    position: absolute;
    display: flex;
    justify-content: space-between;
    align-items: center;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100px;
    background-color: #fff;
    border-top: 1px solid #ccc;
    .action-item {
      flex: 1;
      display: flex;
      gap: 8px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-size: 24px;
      color: #646a73;
    }
  }
}
</style>
