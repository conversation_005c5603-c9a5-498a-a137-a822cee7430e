<script lang="ts" setup>
import { getCourseListApi } from "@/api/customer/exclusiveLink";
import { useSearch } from "@/hooks/useSearch";
import { useRouter } from "vue-router";
import MyList from "@/components/MyList/index.vue";

const props = withDefaults(
  defineProps<{
    immediate?: boolean;
  }>(),
  {
    immediate: true
  }
);

const router = useRouter();

const { list, loading, finished, error, onLoad, onSearch, searchForm } =
  useSearch({
    api: getCourseListApi,
    immediate: props.immediate,
    dataCallback: res => {
      res.list = res.data.list;
      res.total = res.data.total;
    }
  });

const goCode = (id: string) => {
  router.push({
    path: "/customer/newExclusiveLink/qrCode",
    query: {
      id
    }
  });
};

defineExpose({
  onSearch,
  searchForm
});
</script>

<template>
  <div class="h-100%">
    <MyList
      v-model:error="error"
      :list="list"
      :loading="loading"
      :finished="finished"
      :onLoad="onLoad"
    >
      <template #default="{ data }">
        <div class="customer py-4">
          <div class="mb-20px">
            <div>
              {{ data?.name }}
              <span class="tag">课程金额：{{ data?.amount }}</span>
            </div>
          </div>
          <div>
            <div class="qrcode-btn" @click="goCode(data.id)">动态二维码</div>
          </div>
        </div>
      </template>
    </MyList>
  </div>
</template>

<style lang="scss" scoped>
.customer {
  border-bottom: 1px solid #b4b4b4;
  .tag {
    display: inline-block;
    padding: 4px 10px;
    background-color: #fff9e9;
    color: #a2653a;
    font-size: 24px;
    margin-left: 20px;
  }
  .qrcode-btn {
    color: #3370ff;
    font-size: 24px;
    margin-top: 20px;
  }
}
</style>
