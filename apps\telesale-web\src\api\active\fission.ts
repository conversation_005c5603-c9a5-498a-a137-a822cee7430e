/*
 * @Date         : 2024-07-19 10:00:00
 * @Description  : 裂变活动相关接口
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */

import { http } from "/@/utils/http";

// 裂变活动用户参与明细请求参数
export interface FissionUserDetailReq {
  inviterOnionId?: string; // 邀请人洋葱ID
  inviteeOnionId?: string; // 被邀请人洋葱ID/手机号
  bindTimeStart?: string; // 绑定时间开始
  bindTimeEnd?: string; // 绑定时间结束
  isPurchased?: boolean; // 是否购课
  phone?: string; // 被邀请人手机号
  bindTime: any[];
}

// 裂变活动用户参与明细响应数据
export interface FissionUserDetail {
  id: number; // ID
  index: number; // 序号
  inviterOnionId: string; // 邀请人洋葱ID
  inviteeOnionId: string; // 被邀请人洋葱ID/手机号
  bindTime: string; // 绑定时间
  addWechatTime: string; // 成功添加企微时间
  isPurchased: boolean; // 是否购课
  totalAmount: number; // 历史付费总金额
  activityName: string; // 活动名称
  activityTime: string; // 活动时间
  orders?: FissionUserOrder[]; // 订单明细
}

// 裂变活动用户订单明细
export interface FissionUserOrder {
  id: number; // ID
  orderNo: string; // 订单号
  orderName: string; // 订单名称
  payTime: string; // 支付成功时间
  amount: number; // 实付
  salesName: string; // 坐席
  groupName: string; // 所属小组
}

// 获取裂变活动用户参与明细列表
export const getFissionUserDetailListApi = (params: FissionUserDetailReq) => {
  return http.request<{
    list: FissionUserDetail[];
    total: number;
  }>("get", "/api/active/fission/user/detail/list", { params });
};

// 导出裂变活动用户参与明细
export const exportFissionUserDetailApi = (params: FissionUserDetailReq) => {
  return http.request<Blob>("get", "/api/active/fission/user/detail/export", {
    params,
    responseType: "blob"
  });
};

// 获取裂变活动用户订单明细
export const getFissionUserOrdersApi = (params: { userId: string }) => {
  return http.request<{
    list: FissionUserOrder[];
  }>("get", "/api/active/fission/user/orders", { params });
};

// 裂变活动中奖明细请求参数
export interface FissionPrizeDetailReq {
  onionId?: string; // 洋葱ID
  phone?: string; // 手机号
  prizeTimeStart?: string; // 中奖时间开始
  prizeTimeEnd?: string; // 中奖时间结束
  prizeStatus?: number; // 奖品状态
  prizeTime: any[];
}

// 裂变活动中奖明细响应数据
export interface FissionPrizeDetail {
  id: number; // ID
  index: number; // 序号
  onionId: string; // 洋葱ID
  phone: string; // 手机号
  prizeName: string; // 奖品名称
  prizeTime: string; // 中奖时间
  prizeStatus: number; // 奖品状态 0-未领取 1-已领取 2-已过期
  receiveTime: string; // 领取时间
  activityName: string; // 活动名称
  activityTime: string; // 活动时间
}

// 奖品状态选项
export const prizeStatusOptions = [
  {
    label: "未领取",
    value: 0
  },
  {
    label: "已领取",
    value: 1
  },
  {
    label: "已过期",
    value: 2
  }
];

// 获取裂变活动中奖明细列表
export const getFissionPrizeDetailListApi = (params: FissionPrizeDetailReq) => {
  return http.request<{
    list: FissionPrizeDetail[];
    total: number;
  }>("get", "/api/active/fission/prize/detail/list", { params });
};

// 导出裂变活动中奖明细
export const exportFissionPrizeDetailApi = (params: FissionPrizeDetailReq) => {
  return http.request<Blob>("get", "/api/active/fission/prize/detail/export", {
    params,
    responseType: "blob"
  });
};
