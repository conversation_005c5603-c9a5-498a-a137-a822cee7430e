<script lang="ts" setup name="orderSearch">
import { ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import OrderList from "../components/OrderList.vue";

const inputValue = ref<string>();
const orederListRef = ref<InstanceType<typeof OrderList>>();
const route = useRoute();
const router = useRouter();
const onSearch = (val: string) => {
  orederListRef.value!.searchForm[route.query.type as string] = val;
  orederListRef.value?.onSearch();
};

const onCancel = () => {
  router.go(-1);
};

onActivated(() => {
  if (inputValue.value) {
    onSearch(inputValue.value);
  }
});
</script>

<template>
  <div class="container">
    <form action="/">
      <van-search
        v-model="inputValue"
        show-action
        placeholder="请输入搜索关键词"
        @search="onSearch"
        @cancel="onCancel"
      />
    </form>
    <div class="list-content">
      <OrderList ref="orederListRef" :immediate="false" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.container {
  height: 100vh;
  overflow: hidden;
  .list-content {
    padding: 24px;
    padding-top: 0;
    margin: 20px;
    background-color: #fff;
    border-radius: 20px;
    height: calc(100vh - 170px);
    overflow: auto;
  }
}
</style>
