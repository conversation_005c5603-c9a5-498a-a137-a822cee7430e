import { http } from "../utils/http";
import baseURL from "./url";

//飞书卡片预览
export const cardPreview = data => {
  return http.request("post", `${baseURL.api}/web/feishu/preview`, {
    data
  });
};

//模板列表
export const getListTemplate = data => {
  return http.request("post", `${baseURL.api}/web/feishu/templates`, {
    data
  });
};

//模板详情
export const getDetailTemplate = id => {
  return http.request("get", `${baseURL.api}/web/feishu/template/${id}`);
};

//添加模板
export const addTemplate = data => {
  return http.request("post", `${baseURL.api}/web/feishu/template`, {
    data
  });
};

//编辑模板
export const editTemplate = data => {
  return http.request("put", `${baseURL.api}/web/feishu/template/${data.id}`, {
    data
  });
};

//删除模板
export const delTemplate = id => {
  return http.request("delete", `${baseURL.api}/web/feishu/template/${id}`);
};
