/*
 * @Date         : 2025-03-06 16:01:16
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */
import { http } from "../../utils/http";
import baseURL from "../url";

export interface UserReqQuery {
  /**
   * 老用户ID
   */
  userId?: string;
  /**
   * 坐席ID
   */
  workerId?: string;
  /**
   * 开始时间
   */
  startTime?: string;
  /**
   * 结束时间
   */
  endTime?: string;
  platformId?: number;
}

export type PayUserRes = {
  phone?: string;
  onionId?: string;
  amount?: number;
  payTime?: string;
  userId?: string;
  orderId?: string;
}[];

export interface PayUserResBody {
  list: PayUserRes;
}

/**
 * @description 获取某个老用户的业绩详情
 * https://yapi.yc345.tv/project/2071/interface/api/123243
 * <AUTHOR>
 * @date 2025-03-06
 * @export
 * @param {UserReqQuery} params
 * @returns {Promise<PayUserResBody>}
 */
export const getPayUserApi = (params: UserReqQuery) => {
  return http.request<PayUserResBody>(
    `get`,
    `${baseURL.api}/wuhan-marketing/stat/GetOrderListByOldUser`,
    {
      params
    }
  );
};
