<template>
  <div>
    <router-view v-slot="{ Component }">
      <keep-alive :include="keepAliveList">
        <component :is="Component" />
      </keep-alive>
    </router-view>
  </div>
</template>

<script setup lang="ts">
import router from "@/router";
import { preFun } from "@/utils/preFun";
import { storeToRefs } from "pinia";
import { useAppStore } from "@/store/modules/app";

const { keepAliveList } = storeToRefs(useAppStore());

// const keepAliveList = ref<string[]>([])
// for (const item of router.options.routes) {
//   if (item!.meta?.keepAlive && item!.name) {
//     keepAliveList.value.push(item!.name as string)
//   }
// }

onMounted(() => {
  preFun();
});
</script>

<style>
body {
  margin: 0;
  font-size: 28px;
  background-color: #f5f5f5;
}

:root {
  --safe-area-top-own: constant(safe-area-inset-top);
  --safe-area-top-own: env(safe-area-inset-top);
}

@supports not (constant(safe-area-inset-bottom)) {
  :root {
    --safe-area-top-own: 20px;
  }
}
</style>
