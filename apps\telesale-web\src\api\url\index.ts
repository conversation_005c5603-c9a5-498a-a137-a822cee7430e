const baseURL = {
  api: "", //电销后台接口地址
  gearboxDomain: "https://shadow-test.yangcong345.com/api", //中台登录接口地址
  h5: "https://telec-test.yangcong345.com/academicReport.html", //学情报告地址
  shareH5: "https://telec-test.yangcong345.com", // h5地址
  point: "https://track-test.yc345.tv/events/track-pc", //埋点接口地址
  robot: "https://lsy-api-gateway-test.yangcong345.com/telesale/robot-v1",
  aisaas: "https://onionai-api-test.yangcong345.com"
};

switch (import.meta.env.MODE) {
  case "master":
    baseURL.api = "https://api-telesale.yangcong345.com";
    baseURL.h5 = "https://telec.yangcong345.com/academicReport.html";
    baseURL.point = "https://track.yangcong345.com/api/v4/events";
    baseURL.shareH5 = "https://telec.yangcong345.com";
    baseURL.robot = "https://lsy-api-gateway.yangcong345.com/telesale-robot";
    baseURL.aisaas = "https://onionai-api.yangcong345.com";
    break;
  case "stage":
    baseURL.api = "https://api-telesale-stage.yangcong345.com";
    baseURL.h5 = "https://telec-stage.yangcong345.com/academicReport.html";
    baseURL.shareH5 = "https://telec-stage.yangcong345.com";
    baseURL.robot =
      "https://lsy-api-gateway-stage.yangcong345.com/telesale-robot";
    baseURL.aisaas = "https://onionai-api-stage.yangcong345.com";
    break;
  case "test":
    baseURL.api = "https://api-telesale-test.yangcong345.com";
    baseURL.h5 = "https://telec-test.yangcong345.com/academicReport.html";
    baseURL.shareH5 = "https://telec-test.yangcong345.com";
    // baseURL.robot = "https://telesale-robot.wuhan";
    baseURL.robot =
      "https://lsy-api-gateway-test.yangcong345.com/telesale/robot-v1";
    baseURL.aisaas = "https://onionai-api-test.yangcong345.com";
    break;
  default:
    baseURL.api = "https://api-telesale-test.yangcong345.com";
  // baseURL.api = 'http://192.168.67.9:9898';//柯永锋
}
console.log(import.meta.env.MODE, baseURL);
export default baseURL;
