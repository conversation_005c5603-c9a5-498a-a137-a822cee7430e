<script setup lang="ts" name="CardTemplateIndex">
import { ref, computed, onMounted, markRaw } from "vue";
import CardLeft from "./CardLeft.vue";
import CardCenterPreview from "./CardCenterPreview.vue";
import CardRightCommon from "./CardRightCommon.vue";
import Header from "./Header.vue";
import HeaderSet from "./HeaderSet.vue";
import Markdown from "./Markdown.vue";
import Hr from "./Hr.vue";
import Action from "./Action.vue";
import ButtonSet from "./ButtonSet.vue";
import MarkdownSet from "./MarkdownSet.vue";
import Double from "./Double.vue";
import DoubleSet from "./DoubleSet.vue";

const componentObj = {
  Markdown: Markdown,
  Hr: Hr,
  Action: Action,
  ButtonSet: ButtonSet,
  MarkdownSet: MarkdownSet,
  Double: Double,
  DoubleSet: DoubleSet
};

interface Props {
  raw: any;
}

const props = withDefaults(defineProps<Props>(), {});

let loading = ref(false);
let activeModule: any = ref(""); //当前选中的设置模块
let cardPreviewList = ref([]); //卡片预览视图除卡片标题外的数据列表
let cardHeader: any = ref(null); //卡片预览视图除卡片标题数据
let update_multi = ref<boolean>(false); //卡片是否共享

//双击获取Double子元素的位置序号
let divIndex = ref(-1);
//双击获取Markdown或者Double子内容的位置序号
let spanIndex = ref(-1);

//判断卡片预览视图是否为空
let isNullCard = computed(() => {
  return !(cardPreviewList.value.length || cardHeader.value);
});

//判断卡片视图中选择的组件相关的设置组件的名称
let setComponentName = computed(() => {
  if (activeModule.value === "") return "";
  if (activeModule.value === "header") return cardHeader.value.setComponent;
  return cardPreviewList.value[activeModule.value].setComponent;
});

//卡片视图中选择的组件相关的设置组件的key值
let setKey = computed(() => {
  if (activeModule.value === "") return "";
  if (activeModule.value === "header") return "headerSet";
  return (
    cardPreviewList.value[activeModule.value].key + "Set" + activeModule.value
  );
});

//获取转换后的飞书卡片内容
function getCardObj() {
  return {
    card: cardCenterPreviewRef.value.transferFormat(),
    raw: JSON.stringify({
      config: {
        enable_forward: true,
        update_multi: update_multi.value
      },
      cardHeader: cardHeader.value,
      cardPreviewList: cardPreviewList.value
    })
  };
}
//获取点击的卡片模块的位置序号
function setModule(key) {
  activeModule.value = key;
  spanIndex.value = -1;
  divIndex.value = -1;
}
//取消卡片视图中的选择
function cancelSelect() {
  activeModule.value = "";
  spanIndex.value = -1;
  divIndex.value = -1;
}

//卡片回显
function cardViewEcho() {
  let card = JSON.parse(props.raw);
  update_multi.value = card.config?.update_multi || false;
  if (card.cardHeader) {
    card.cardHeader.setComponent = markRaw(HeaderSet);
    cardHeader.value = card.cardHeader;
  }

  card.cardPreviewList.forEach(item => {
    let key = "",
      keySet = "";
    if (item.key === "action_button") {
      key = "Action";
      keySet = "ButtonSet";
    } else {
      key = item.key[0].toUpperCase() + item.key.slice(1);
      keySet = key + "Set";
    }
    item.componentName = markRaw(componentObj[key]);
    if (item.setComponent) {
      item.setComponent = markRaw(componentObj[keySet]);
    }
  });
  cardPreviewList.value = card.cardPreviewList;
}

let cardCenterPreviewRef = ref();
onMounted(() => {
  props.raw && cardViewEcho();
});

defineExpose({
  isNullCard,
  getCardObj
});
</script>
<template>
  <div class="c-index-page" v-loading="loading">
    <CardLeft
      ref="cardLeftRef"
      v-model:cardHeader="cardHeader"
      v-model:cardPreviewList="cardPreviewList"
      v-model:update_multi="update_multi"
    />
    <div class="c-index-center">
      <CardCenterPreview
        ref="cardCenterPreviewRef"
        v-model:loading="loading"
        v-model:cardHeader="cardHeader"
        v-model:cardPreviewList="cardPreviewList"
        @cancelSelect="cancelSelect"
        :isNullCard="isNullCard"
        :update_multi="update_multi"
      />
      <div class="c-card">
        <p class="c-card-title">卡片预览</p>
        <div class="c-card-container c-card-box" v-show="isNullCard">
          暂无内容。从左栏中选取模块，开始搭建卡片吧!
        </div>
        <div class="c-card-container" v-show="!isNullCard">
          <Header
            ref="header"
            v-if="cardHeader"
            @click="setModule('header')"
            v-model:cardHeader="cardHeader"
          />
          <div class="c-card-box" v-show="cardPreviewList.length">
            <div
              v-for="(item, index) in cardPreviewList"
              class="c-card-list"
              :class="{ 'c-select-active': activeModule === index }"
              :key="index"
              @click="setModule(index)"
            >
              <component
                :is="item.componentName"
                :key="item.ref"
                v-model:spanIndex="spanIndex"
                v-model:divIndex="divIndex"
                v-model:initData="cardPreviewList[index]"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="c-index-right">
      <div class="c-set-title">
        <p>模块组件配置</p>
        <el-button @click="cancelSelect">取消选择</el-button>
      </div>
      <div class="c-set-container" v-show="activeModule !== ''">
        <CardRightCommon
          ref="cardRightInfoRef"
          v-model:activeModule="activeModule"
          v-model:loading="loading"
          v-model:cardHeader="cardHeader"
          v-model:cardPreviewList="cardPreviewList"
        />
        <component
          v-if="setComponentName"
          :key="setKey"
          :is="setComponentName"
          :initData="
            setKey === 'headerSet' ? cardHeader : cardPreviewList[activeModule]
          "
          v-model:spanIndex="spanIndex"
          v-model:divIndex="divIndex"
        />
      </div>
      <div class="c-set-container c-set-null" v-show="activeModule === ''">
        请点击选择模块组件
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@mixin hover() {
  border: 1px solid #dee0e3;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(31, 35, 41, 0.16);
}

.c-select-active {
  @include hover;
}

.c-index-page {
  display: flex;
  height: 100%;

  .c-index-center {
    width: 400px;
    background-color: #f5f6f7;

    .c-card {
      width: 308px;
      margin: 0 auto 20px;
      padding: 12px 0 32px;
      height: calc(100% - 68px);
      overflow-y: scroll;

      &::-webkit-scrollbar-track,
      &::-webkit-scrollbar-thumb {
        background: transparent;
      }

      .c-card-title {
        font-size: 16px;
        line-height: 24px;
        font-weight: 500;
        margin-left: 1px;
        margin-bottom: 8px;
      }

      .c-card-container {
        border-radius: 8px;
        background-color: #fff;
        border: 1px solid #dee0e3;
        user-select: none;
      }

      .c-card-box {
        padding: 6px 6px;
        line-height: 20px;
        font-weight: 400;
        color: #1f2329;

        .c-card-list {
          padding: 6px;
          border: 1px solid transparent;
          cursor: default;

          &:hover {
            @include hover;
          }
        }
      }
    }
  }

  .c-index-right {
    flex: auto;
    background-color: #fff;

    .c-set-title {
      padding: 16px;
      font-size: 16px;
      font-weight: 500;
      background-color: #fff;
      position: sticky;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .c-set-null {
      height: 200px;
      line-height: 200px;
      text-align: center;

      font-size: 18px;
      color: #999999;
    }

    .c-set-container {
      padding: 0 16px;
      height: calc(100% - 40px);
      overflow-y: auto;
    }
  }
}
</style>
