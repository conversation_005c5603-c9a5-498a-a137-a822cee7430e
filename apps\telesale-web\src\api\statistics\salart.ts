/*
 * @Date         : 2025-01-06 16:54:41
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */
import { http } from "../../utils/http";
import baseURL from "../url";

export interface PostWebAnalyFamilySalaryListReqBodyOther {
  begin?: number;
  end?: number;
  workerid?: string;
  orgId?: number;
  /**
   * 家庭类型
   */
  familyCategory?: string;
  [k: string]: unknown;
}

export interface PostWebAnalyFamilySalaryListResBody {
  /**
   * 坐席id
   */
  workerId?: number;
  /**
   * 拨打家庭数
   */
  callNumWithFamily?: number;
  /**
   * 拨打线索数
   */
  callNumWithInfo?: number;
  /**
   * 拨打手机号数
   */
  callNumWithPhone?: number;
  /**
   * 拨打次数
   */
  callNum?: number;
  /**
   * 家庭有效接通量
   */
  effectiveNumWithFamily?: number;
  /**
   * 家庭有效接通率
   */
  effectiveRateWithFamily?: number;
  /**
   * 家庭有效通话时长
   */
  effectiveCallLengthWithFamily?: number;
  /**
   * 家庭转换数
   */
  callConversionNumWithFamily?: number;
  /**
   * 家庭转化金额
   */
  callConversionFeeWithFamily?: number;
  /**
   * 家庭拨打转换率
   */
  callConversionRateWithFamily?: number;
  /**
   * 家庭拨打转化客单价
   */
  callConversionAvgWithFamily?: number;
  /**
   * 家庭拨打转化arpu
   */
  callConversionARPUWithFamily?: number;
  [k: string]: unknown;
}

/**
 * @description 家庭绩效数据统计
 * https://yapi.yc345.tv/project/1415/interface/api/118868
 * <AUTHOR>
 * @date 2025-01-06
 * @export
 * @param {PostWebAnalyFamilySalaryListReqBodyOther} data
 * @returns {Promise<PostWebAnalyFamilySalaryListResBody>}
 */
export const getFamilySalaryApi = (
  data: PostWebAnalyFamilySalaryListReqBodyOther
) => {
  return http.request<PostWebAnalyFamilySalaryListResBody>(
    `post`,
    `${baseURL.api}/web/analy/family_salary/list`,
    {
      data
    }
  );
};

// 导出家庭绩效
export const exportFamilySalaryApi = (
  data: PostWebAnalyFamilySalaryListReqBodyOther
) => {
  return http.request("post", `${baseURL.api}/web/analy/family_salary/export`, {
    data
  });
};
