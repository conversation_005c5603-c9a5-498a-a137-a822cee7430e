<script lang="ts" setup>
import { ref } from "vue";
import { showLoadingToast, closeToast } from "vant";
import { getCallRecordApi, getUserRecordApi } from "@/api/customer/details";
import { CallRecordType } from "@/types/customer/details";
import { getLabel, timeChange } from "@/utils/common";
import { intentionApi } from "@/api/customer/action";

const props = defineProps<{
  infoUuid: string;
}>();

const data = ref<CallRecordType[]>([]);
const userInfo = ref({
  description: ""
});

const getWatchRecord = () => {
  const parmas = {
    infoUuid: props.infoUuid,
    pageIndex: 1,
    pageSize: 100
  };
  showLoadingToast({});
  getCallRecordApi(parmas)
    .then(res => {
      data.value = res.list;
    })
    .catch(() => {
      data.value = [];
    })
    .finally(() => {
      closeToast();
    });
};

const intentionList = ref<LabelValueOption[]>([]);
const getIntentionList = () => {
  intentionApi().then(res => {
    if (res) {
      intentionList.value = res.map(item => {
        return {
          label: Object.keys(item)[0],
          value: Object.values(item)[0]
        };
      });
    }
  });
};

const getUserRecord = () => {
  getUserRecordApi({
    infoUuid: props.infoUuid
  }).then(res => {
    userInfo.value.description = res.description;
  });
};

getIntentionList();
getWatchRecord();
getUserRecord();
onActivated(() => {
  getWatchRecord();
});
</script>

<template>
  <div class="documentary-record">
    <div class="px-20px mb-40px">
      <div class="text-32px">用户信息记录：</div>
      <div class="card" style="white-space: pre-wrap">
        {{ userInfo.description || "无" }}
      </div>
    </div>
    <div class="px-20px text-32px">跟单记录：</div>
    <van-steps
      v-if="data.length > 0"
      active-color="#00a1ff"
      inactive-color="#00a1ff"
      direction="vertical"
      :active="0"
    >
      <van-step v-for="(item, index) in data" :key="index">
        <template #active-icon>
          <div class="radio" />
        </template>
        <template #inactive-icon>
          <div class="radio" />
        </template>
        <div class="step-content">
          <div>{{ timeChange(item.createdAt, 3) }}</div>
          <div class="card">
            <span class="tag">
              {{ getLabel(item.intention, intentionList) }} ｜
            </span>
            <span>{{ item.note }}</span>
            <div v-if="item.notifyTime">
              <span>下次跟单时间：</span>
              <span>{{ timeChange(item.notifyTime * 1000, 3) }}</span>
            </div>
          </div>
        </div>
      </van-step>
    </van-steps>
    <van-empty v-else description="暂无数据" />
  </div>
</template>

<style lang="scss" scoped>
.documentary-record {
  background-color: #fff;
  padding-top: 20px;
  .step-content {
    color: #000;
  }
  .radio {
    width: 20px;
    height: 20px;
    background: #00a1ff;
    border-radius: 50%;
    display: inline-block;
  }
  .card {
    padding: 30px;
    margin-top: 20px;
    border: 1px solid #ccc;
    box-shadow: -3px 0 10px rgba(0, 0, 0, 0.2);
    line-height: 48px;
    margin-bottom: 20px;
    .tag {
      color: #22b2ee;
    }
  }
}
</style>
