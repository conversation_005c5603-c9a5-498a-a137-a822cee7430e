<!--
 * @Date         : 2024-10-18 16:09:53
 * @Description  : 加购平板
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { getHasIpadApi } from "@/api/customer/details";
import { IpadGoodInfo } from "@telesale/shared/src/businessHooks/payPush/types";
import {
  isStagesList,
  stagesType
} from "@telesale/shared/src/data/exclusiveLink";
import { getLabel } from "@telesale/shared";
import MySelect from "@/components/MySelect/index.vue";
import { cloneDeep } from "lodash-es";
import { getInstallmentPayType } from "@telesale/shared/src/businessHooks/payPush/installmentPay";
import { IpadReq, padLinkApi } from "@/api/customer/exclusiveLink";
import { getArrayBufferBase64 } from "@/utils/common";
import { showLoadingToast, showToast } from "vant";

const props = defineProps<{
  userId: string;
}>();

const loading = ref(false);
const dialogVisible = ref(false);
const imgUrl = ref("");
const padList = ref<IpadGoodInfo[]>([]);
const form = ref<IpadReq>({
  pad: "",
  isInstallment: 2,
  installmentPayType: []
});

const padAmount = computed(() => {
  return getLabel(form.value.pad, padList.value, "amount", "label");
});

const changeInstallment = () => {
  form.value.installmentPayType = ["alipayFq"];
};

const getInfo = () => {
  loading.value = true;
  getHasIpadApi({ userId: props.userId })
    .then(res => {
      padList.value = res.data.ok ? res.data.pads : [];
    })
    .finally(() => {
      loading.value = false;
    });
};

const submit = async () => {
  loading.value = true;
  const params = cloneDeep(form.value);
  params.installmentPayType = getInstallmentPayType(
    form.value.isInstallment as number,
    form.value.installmentPayType as string[]
  );
  showLoadingToast({});
  padLinkApi(params)
    .then(async res => {
      const url = await getArrayBufferBase64(res);
      imgUrl.value = url;
      showToast("操作成功");
      dialogVisible.value = true;
    })
    .finally(() => {
      loading.value = false;
    });
};

getInfo();
</script>

<template>
  <div>
    <van-form required class="mt-20px" @submit="submit">
      <van-cell-group inset>
        <MySelect
          v-model:value="form.pad"
          label="加购商品"
          placeholder="请选择加购商品"
          :columns="padList"
          :options="{
            label: 'label',
            value: 'label'
          }"
          :rules="[
            { required: true, message: '请选择加购商品', trigger: 'onChange' }
          ]"
        />
        <van-field
          name="radio"
          label="分期支付"
          required
          :rules="[
            {
              required: true,
              message: '请选择分期支付',
              trigger: 'onChange'
            }
          ]"
        >
          <template #input>
            <van-radio-group
              v-model="form.isInstallment"
              direction="horizontal"
              @change="changeInstallment"
            >
              <van-radio
                v-for="(item, index) in isStagesList"
                :key="index"
                :name="item.value"
              >
                {{ item.label }}
              </van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field
          v-if="form.isInstallment === 1"
          name="checkboxGroup"
          label="分期支付方式"
          required
          :rules="[
            {
              required: true,
              message: '请选择分期支付方式',
              trigger: 'onChange'
            }
          ]"
        >
          <template #input>
            <van-checkbox-group
              v-model="form.installmentPayType"
              direction="horizontal"
              class="gap-5px"
            >
              <van-checkbox
                v-for="(item, index) in stagesType"
                :key="index"
                :name="item.value"
                shape="square"
              >
                {{ item.label }}
              </van-checkbox>
            </van-checkbox-group>
          </template>
        </van-field>
        <van-field label="商品售价" readonly v-model="padAmount" />
      </van-cell-group>
      <div style="margin: 16px" class="flex gap-20px">
        <van-button class="flex-1" type="primary" native-type="submit">
          生成二维码
        </van-button>
      </div>
    </van-form>
    <SaveImageDialog v-model:show="dialogVisible" :imgUrl="imgUrl" />
  </div>
</template>

<style lang="scss" scoped></style>
