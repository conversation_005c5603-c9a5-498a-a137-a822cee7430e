/*
 * @Date         : 2024-11-15 14:24:36
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "@/utils/http";

// 上传文件
export const uploadFileApi = (data: FormData) => {
  return http.request<string>("post", `/web/upload/object`, {
    data
  });
};

// 获取文件地址
export const getFileApi = (params: { object: string }) => {
  return http.request<string>("get", `/web/crm/voucher`, {
    params
  });
};

// 是否是实验组坐席
export const isExperimentGroupWorkerApi = (params: { workerId: number }) => {
  return http.request<{
    data: {
      isExperimentGroupWorker: boolean;
    };
  }>(
    "get",
    `/wuhan-datapool/admin/isExperimentGroupWorker`,
    {
      params
    },
    {
      isData: true
    }
  );
};

/**
 * @description: 转发接口
 * @param {string} target 目标服务
 * @param {string} targetPath 目标路径
 */
export const forwardApi = (params: {
  target?: string;
  host?: string;
  targetPath: string;
  method?: "get" | "post";
  [x: string]: any;
}) => {
  return http.request<any>(
    params.method || "get",
    `/web/third_party/proxy`,
    {
      params,
      data: params
    },
    {
      showErrorToast: params.hideError
    }
  );
};
