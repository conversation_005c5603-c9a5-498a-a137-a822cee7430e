/*
 * @Author: xiaozhen <EMAIL>
 * @Date: 2025-04-25 12:10:43
 * @LastEditors: xiaozhen <EMAIL>
 * @LastEditTime: 2025-04-25 12:19:48
 * @FilePath: /telesale-web_v2/apps/telesale-web/src/api/active/themeConfig.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { http } from "../../utils/http";
import baseURL from "../url";

export interface ExternalUserListExternalPageReqQuery {
  pageIndex?: string;
  pageSize?: string;
  name?: string;
}

export interface ExternalUserListExternalPageResBody {
  list: {
    id?: number;
    qrCodeId?: number;
    imageUrl?: string;
    createdAt?: string;
    updatedAt?: string;
    createdBy?: number;
  }[];
  total: number;
}

/**
 * @description 引流页配置列表
 * https://yapi.yc345.tv/project/2331/interface/api/125912
 * <AUTHOR>
 * @date 2025-04-25
 * @export
 * @param {ExternalUserListExternalPageReqQuery} params
 * @returns {Promise<ExternalUserListExternalPageResBody>}
 */
export const getExternalConfigListApi = (
  params: ExternalUserListExternalPageReqQuery
) => {
  return http.request<ExternalUserListExternalPageResBody>(
    `get`,
    `${baseURL.api}/wuhan-miniprogram/externalUser/listExternalPage`,
    {
      params
    }
  );
};

export interface ExternalPageReqBody {
  id?: number;
  qrCodeId?: number;
  imageUrl?: string;
  name?: string;
}

/**
 * @description 创建引流页配置
 * https://yapi.yc345.tv/project/2331/interface/api/125908
 * <AUTHOR>
 * @date 2025-04-25
 * @export
 * @param {ExternalPageReqBody} data
 */
export const createExternalConfigApi = (data: ExternalPageReqBody) => {
  return http.request(
    `post`,
    `${baseURL.api}/wuhan-miniprogram/externalUser/createExternalPage`,
    {
      data
    }
  );
};

/**
 * @description 更新引流页配置
 * @param {ExternalPageReqBody} data
 */
export const updateExternalConfigApi = (data: ExternalPageReqBody) => {
  return http.request(
    `put`,
    `${baseURL.api}/wuhan-miniprogram/externalUser/updateExternalPage`,
    {
      data
    }
  );
};

/**
 * @description 更新引流页配置
 * @param {ExternalPageReqBody} data
 */
export const getExternalConfigInfoApi = (params: { id: number }) => {
  return http.request<ExternalPageReqBody>(
    `get`,
    `${baseURL.api}/wuhan-miniprogram/externalUser/getExternalPage`,
    {
      params
    }
  );
};

/**
 * @description 删除引流页配置
 * https://yapi.yc345.tv/project/2331/interface/api/125909
 * <AUTHOR>
 * @date 2025-04-25
 * @export
 */
export const deleteExternalConfigApi = (params: { id: number }) => {
  return http.request(
    `delete`,
    `${baseURL.api}/wuhan-miniprogram/externalUser/deleteExternalPage`,
    {
      params
    }
  );
};
