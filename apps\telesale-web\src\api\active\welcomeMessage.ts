/*
 * @Date         : 2024-02-28 13:53:11
 * @Description  : 欢迎语相关api
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "/@/utils/http";
import baseURL from "../url";
import {
  WelcomeMessageRes,
  WelcomeMessageQuery,
  WelcomeMessageForm
} from "/@/types/active/welcomeMessage";

/**
 * @description: 获取欢迎语列表
 * @returns {WelcomeMessageRes}
 */
export const getWelcomeListApi = (params?: WelcomeMessageQuery) => {
  return http.request<{
    list: WelcomeMessageRes[];
    total: number;
  }>("get", `${baseURL.api}/web/welcome_msg/list`, { params });
};

/**
 * @description: 获取欢迎语详情
 * @param {number} id
 * @returns {WelcomeMessageRes}
 */
export const getWelcomeMessageInfoApi = (id: number) => {
  return http.request<{ welcomeMsg: WelcomeMessageRes }>(
    "get",
    `${baseURL.api}/web/welcome_msg/get`,
    {
      params: {
        id
      }
    }
  );
};

/**
 * @description: 新增欢迎语
 * @param {WelcomeMessageForm} data
 */
export const addWelcomeMessageApi = (data: WelcomeMessageForm) => {
  return http.request("post", `${baseURL.api}/web/welcome_msg/create`, {
    data
  });
};

/**
 * @description: 编辑欢迎语
 * @param {WelcomeMessageForm} data
 */
export const updateWelcomeMessageApi = (data: WelcomeMessageForm) => {
  return http.request("post", `${baseURL.api}/web/welcome_msg/update`, {
    data
  });
};

/**
 * @description: 删除欢迎语
 * @param {number} id
 */
export const delWelcomeMessageApi = (id: number) => {
  return http.request("delete", `${baseURL.api}/web/welcome_msg/delete`, {
    params: { id }
  });
};
