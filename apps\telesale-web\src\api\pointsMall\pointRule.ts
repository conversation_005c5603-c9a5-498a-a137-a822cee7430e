/*
 * @Date         : 2024-05-27 12:12:37
 * @Description  : 积分规则设置
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */
import { http } from "/@/utils/http";
import baseURL from "../url";

interface PointRuleRes {
  rule: string;
  activeTime: string;
  updateAt: string;
}

export interface PointRuleJson {
  type: number;
  enable?: boolean;
  dayLimit?: number;
  uploadInterval?: number;
  activeBgImage?: string[];
  rule?: Rule[];
}

export interface Rule {
  points: number;
  conditions: Condition[];
}

export interface Condition {
  operator: "gt" | "eq" | "lte";
  value: number;
}

/**
 * @description: 获取积分规则
 * @returns { PointRuleRes }
 */
export const getPointRuleApi = () => {
  return http.request<PointRuleRes>(
    "get",
    `${baseURL.api}/wuhan-marketing/point/rule`
  );
};

/**
 * @description: 创建保存积分规则
 * @param {Pick<PointRuleRes, 'rule'>} data
 */
export const updatePointRuleApi = (data: Pick<PointRuleRes, "rule">) => {
  return http.request("post", `${baseURL.api}/wuhan-marketing/point/rule`, {
    data
  });
};
