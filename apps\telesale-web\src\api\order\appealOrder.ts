/*
 * @Date         : 2024-03-14 16:13:18
 * @Description  : 审核订单相关api
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";
import baseURL from "../url";
import {
  AppealForm,
  AppealOrderInfo,
  AppealOrderReq,
  AppealOrderRes,
  AppealRecord
} from "/@/types/order/appealOrder";

/**
 * @description: 获取申诉订单列表
 * @param {AppealOrderReq} data
 * @returns {AppealOrderRes}
 */
export const getAppealOrderApi = (data: AppealOrderReq & PageInfo) => {
  return http.request<{
    list: AppealOrderRes[];
    total: number;
  }>("post", `${baseURL.api}/sync-order/appeal_order/list`, {
    data
  });
};

/**
 * @description: 申诉订单详情
 * @param {number} id
 * @returns {}
 */
export const getAppealInfoApi = (id: number) => {
  return http.request<AppealOrderInfo>(
    "get",
    `${baseURL.api}/sync-order/appeal_order`,
    {
      params: { id }
    }
  );
};

/**
 * @description: 申诉订单的审核记录
 * @param {number} id
 * @returns {}
 */
export const getAppealRecordApi = (params: { id: number }) => {
  return http.request<{
    list: AppealRecord[];
  }>("get", `${baseURL.api}/sync-order/appeal/history`, {
    params
  });
};

/**
 * @description: 审核订单
 * @param {AppealForm} data
 * @returns {}
 */
export const appealOrderApi = (data: AppealForm) => {
  return http.request("post", `${baseURL.api}/web/appeal/done`, {
    data
  });
};

/**
 * @description: 获取下一条申诉订单
 * @param {AppealOrderReq} data
 * @returns {}
 */
export const getAppealNextApi = (data: AppealOrderReq & { id: number }) => {
  return http.request<AppealOrderInfo>(
    "post",
    `${baseURL.api}/sync-order/appeal_order/next`,
    {
      data
    }
  );
};
