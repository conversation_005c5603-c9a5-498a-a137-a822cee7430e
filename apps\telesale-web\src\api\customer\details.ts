/*
 * @Date         : 2024-02-29 18:41:50
 * @Description  : 线索详情相关api
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */

import { IpadGoodInfo } from "@telesale/shared/src/businessHooks/payPush/types";
import { http } from "../../utils/http";
import baseURL from "../url";
import { GoodsConfig } from "/@/types/customer/goodsConfig";

interface UserRoleReq {
  infoUuid: string;
  role: string;
}

/**
 * @description: 修改用户身份
 * @param {LeafNodeQuery} params
 * @returns {LeafNodeRes}
 */
export const updateUserRoleApi = (data: UserRoleReq) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/workerAllocateInfo/updateRole`,
    {
      data
    }
  );
};

export interface TrailOrder {
  orderId: string;
  userId: string;
  deviceLock: boolean;
  trialStartTime: string;
  trialEndTime: string;
  status: string;
}

/**
 * @description: 获取体验机信息
 * @param {userId} string
 * @returns {trialOrderInfos}
 */
export const getTrialOrderListApi = (params: { userId: string }) => {
  return http.request<{
    trialOrderInfos: TrailOrder[];
  }>("get", `${baseURL.api}/sync-order/order/get_user_trial_order_info`, {
    params
  });
};

/**
 * @description: 获取用户是否可以支持加购平板
 * @param {userId} string
 * @returns {ok} boolean
 */
export const getHasIpadApi = (params: { userId: string }) => {
  return http.request<{
    ok: boolean;
    pads: IpadGoodInfo[];
  }>("get", `${baseURL.api}/web/pushOrder/pad-sale/join`, {
    params
  });
};

/**
 * @description: 获取学段商品等信息
 * @param {userId} string
 * @returns {trialOrderInfos}
 */
export const getGoodConfigApi = () => {
  return http.request<{
    data: {
      stage: GoodsConfig[];
      msg: string;
      url: string;
    };
  }>("get", `${baseURL.api}/web/pushOrder/good/config`);
};

export interface GiveProfile {
  userId: string;
  ids: number[];
}

/**
 * @description: 赠送资料
 * @param {GiveProfile} data
 */
export const giveProfileApi = data => {
  return http.request<{
    failNum: number;
    successNum: number;
  }>("post", `${baseURL.api}/wuhan-datapool/materials/send`, {
    data
  });
};

/**
 * @description: 获取资料发送记录
 * @param {string} userId
 */
export const giveProfileRecordApi = (params: { userId: string }) => {
  return http.request<{
    list: {}[];
    sendRecord: {
      materialName: string;
      giftAt: number;
      giftTime: string;
      userId: string;
      workerId: number;
    }[];
  }>("get", `${baseURL.api}/wuhan-datapool/materials/send_record`, {
    params
  });
};

interface GiveVip {
  userId: string;
  addDay: number;
}

/**
 * @description: 赠送体验版大会员
 * @param {GiveVip} data
 */
export const giveBigVipApi = (data: GiveVip) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/info/allocation/send_trial_big_vip`,
    {
      data
    }
  );
};

export interface NewQue {
  regionCode: string;
}

/**
 * @description: 获取新题型字段
 * @param {NewQue} params
 * @returns {boolean}
 */
export const getNewQueApi = (params: NewQue) => {
  return http.request<{ isNewExamArea: boolean }>(
    "get",
    `${baseURL.api}/wuhan-datapool/workerAllocateInfo/isNewExamArea`,
    { params }
  );
};

/**
 * @description: 获取用户信息记录
 * @param { string } infoUuid
 * @returns { string } note
 */
export const getUserRecordApi = (params: { infoUuid: string }) => {
  return http.request<{
    description: string;
  }>("get", `${baseURL.api}/wuhan-datapool/info/GetInfoDescription`, {
    params
  });
};

/**
 * @description: 设置用户信息记录
 * @param { string } infoUuid
 * @param { string } description
 * @returns { string } description
 */
export const setUserRecordApi = (data: {
  infoUuid: string;
  description: string;
}) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/info/SaveInfoDescription`,
    {
      data
    }
  );
};

/**
 * @description: 查询用户手机号变更记录
 * @param {string} userId
 * @returns {boolean}
 */
export const getChangePhoneApi = (params: { userId: string } & PageInfo) => {
  return http.request<{
    list: {
      id: string;
      createdAt: string;
      updatedAt: string;
      userId: string;
      type: string;
      operator: string;
      beforeInfo: string;
      afterInfo: string;
    }[];
  }>(
    "get",
    `${baseURL.api}/wuhan-datapool/workerAllocateInfo/listUserChangeLog`,
    { params }
  );
};

/**
 * @description: 根据initialPhone查询线索
 * @param {string} initialPhone
 * @returns {boolean}
 */
export const getPhoneInfoApi = (params: { initialPhone: string }) => {
  return http.request<{
    list: {
      id: string;
      createdAt: string;
      updatedAt: string;
      userId: string;
      type: string;
      operator: string;
      beforeInfo: string;
      afterInfo: string;
    }[];
  }>(
    "get",
    `${baseURL.api}/wuhan-datapool/workerAllocateInfo/findByInitialPhone`,
    { params }
  );
};

/**
 * @description: 查看用户风险
 * @returns {boolean}
 */
export const getPhoneWhiteApi = (params: {
  userId?: string;
  phone?: string;
}) => {
  return http.request<{
    hasRisk: boolean;
  }>("get", `${baseURL.api}/wuhan-datapool/workerAllocateInfo/checkRisk`, {
    params
  });
};

/*
 * @description: 用户加入Ai定制班
 * @param { string } userId
 * @returns
 */
export const setAiPlanApi = (data: { userId: string }) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/info/createStudyPlan`,
    {
      data
    }
  );
};

/**
 * @description: 用户是否加入Ai定制班
 * @param { string } userId
 * @returns
 */
export const getAiPlanApi = (params: { userId: string }) => {
  return http.request<{
    isStudyPlan: boolean;
  }>("get", `${baseURL.api}/wuhan-datapool/info/getStudyPlan`, {
    params
  });
};

/**
 * @description: 是否是AI定制班实验组坐席
 * @param { number } workerId
 * @returns
 */
export const getWorkerPlanApi = (data: { workerId: number }) => {
  return http.request<{
    isStudyPlan: boolean;
  }>("post", `${baseURL.api}/wuhan-worker/worker/isStudyPlan`, {
    data
  });
};

export interface StudyWeekInfo {
  weekCompletionRate: string;
  lastWeekCompletionRate: string;
  weekCompletionRateGrowth: string;
  weekStudyDuration: string;
  lastWeekStudyDuration: string;
  weekStudyDurationGrowth: string;
}

/**
 * @description: 用户学习周统计
 * @param { number } workerId
 * @returns
 */
export const getUserStudyWeekStatisticApi = (params: {
  uid: string;
  startTime: string;
  endTime: string;
}) => {
  return http.request<StudyWeekInfo>(
    "get",
    `${baseURL.api}/web/proxy/user/study_week_statistic`,
    {
      params
    }
  );
};

interface StudyPlanInfo {
  contentId: string;
  contentName: string;
  studyFlowDoneTime: string;
  studyFlowWeekDay: string;
  classId: string;
  className: string;
  publisherLabel: PublisherLabel;
  sourceType: string;
  sourceName: string;
  priorityLabel: PublisherLabel;
  weekLabel: PublisherLabel;
  customLabels: any[];
  studyProgress: number;
  studyDuration: string;
}

interface PublisherLabel {
  id: string;
  type: string;
  sourceId: string;
  sourceType: string;
  pid: string;
  name: string;
  image: string;
  sort: number;
  createdUser: string;
  createdTime: string;
  updatedUser: string;
  updatedTime: string;
  aliasName: string;
}

/**
 * @description: 用户学习计划
 * @param { number } workerId
 * @returns
 */
export const getUserStudyPlanApi = (params: { uid: string; date: string }) => {
  return http.request<{
    studyContents: StudyPlanInfo[];
  }>("get", `${baseURL.api}/web/proxy/user/study_plan_detail`, {
    params
  });
};

export function getIframeTokenApi(params: {
  userId: string;
  [k: string]: unknown;
}) {
  return http.request<{ token: string; userId: string }>(
    "get",
    `${baseURL.api}/web/proxy/user/statistic_study_token`,
    { params }
  );
}

export interface GetSyncOrderDiscoveryPageAuthReqQuery {
  /**
   * 用户id
   */
  userId: string;
}

export interface GetSyncOrderDiscoveryPageAuthResBody {
  hasAuth?: boolean;
}

/**
 * @description 查询用户是否是实验组
 * https://yapi.yc345.tv/project/1415/interface/api/118343
 * @date 2024-12-26
 * @export
 * @param {GetSyncOrderDiscoveryPageAuthReqQuery} params
 * @returns {Promise<GetSyncOrderDiscoveryPageAuthResBody>}
 */
export function getDiscoveryPageAuthApi(
  params: GetSyncOrderDiscoveryPageAuthReqQuery
) {
  return http.request<GetSyncOrderDiscoveryPageAuthResBody>(
    "get",
    `${baseURL.api}/sync-order/discovery_page/auth`,
    {
      params
    }
  );
}

export interface AbLearningReqQuery {
  /**
   * 用户id
   */
  userId: string;
}

export interface AbLearningResBody {
  /**
   * true: 有，false：无
   */
  isA: boolean;
}

/**
 * @description 学习页实验组
 * https://yapi.yc345.tv/project/2352/interface/api/125951
 * <AUTHOR>
 * @date 2025-04-29
 * @export
 * @param {AbLearningReqQuery} params
 * @returns {Promise<AbLearningResBody>}
 */
export const getAbLearningApi = (params: AbLearningReqQuery) => {
  return http.request<AbLearningResBody>(
    `get`,
    `${baseURL.api}/wuhan-datapool/info/ab/learning`,
    {
      params
    }
  );
};

export interface GetSyncOrderLeaveListResBody {
  leaves?: {
    id: number;
    leave: string;
  }[];
}

/**
 * @description 获取留言模板列表
 * https://yapi.yc345.tv/project/1415/interface/api/118350
 * @date 2024-12-26
 * @export
 * @returns {Promise<GetSyncOrderLeaveListResBody>}
 */
export const getSyncOrderLeaveListApi = () => {
  return http.request<GetSyncOrderLeaveListResBody>(
    `get`,
    `${baseURL.api}/sync-order/leave/list`
  );
};

/**
 * @description 发送留言卡片
 */
export const sendMessageCardApi = (data: {
  id: number;
  workerId: number;
  userId: string;
}) => {
  return http.request(`post`, `${baseURL.api}/sync-order/leave/send`, { data });
};

export interface GetSyncOrderLeaveRecordListReqQuery {
  /**
   * 用户id
   */
  userId: string;
}

export interface GetSyncOrderLeaveRecordListResBody {
  leaveRecords?: {
    id: number;
    userId: string;
    pushAt: number;
    workerId: number;
  }[];
  list: {
    id: number;
    userId: string;
    pushAt: number;
    workerId: number;
  }[];
}

/**
 * @description 获取推送记录
 * https://yapi.yc345.tv/project/1415/interface/api/118357
 * @date 2024-12-26
 * @export
 * @param {GetSyncOrderLeaveRecordListReqQuery} params
 * @returns {Promise<GetSyncOrderLeaveRecordListResBody>}
 */
export const getSyncOrderLeaveRecordListApi = (
  params: GetSyncOrderLeaveRecordListReqQuery
) => {
  return http.request<GetSyncOrderLeaveRecordListResBody>(
    `get`,
    `${baseURL.api}/sync-order/leave_record/list`,
    {
      params
    }
  );
};

export function getUserCoolApi(params: { userId: string }) {
  return http.request<{ hasCoolDownDuration: boolean; cdExpire: string }>(
    "get",
    `${baseURL.api}/wuhan-datapool/workerAllocateInfo/getUserCoolDownDuration`,
    { params }
  );
}

export interface SchoolDataReqBodyOther {
  city?: string;
  regionCode?: string;
}

export interface SchoolDataResBody {
  schools?: {
    schoolId?: number;
    schoolName?: string;
    province?: string;
    city?: string;
    area?: string;
    cooperateYears?: number;
  }[];
  regUsers?: number;
}

/**
 * @description 入校数据
 * https://yapi.yc345.tv/project/2672/interface/api/124306
 * <AUTHOR>
 * @date 2025-03-25
 * @export
 * @param {SchoolDataReqBodyOther} data
 * @returns {Promise<SchoolDataResBody>}
 */
export const getSchoolDataApi = (data: SchoolDataReqBodyOther) => {
  return http.request<SchoolDataResBody>(
    `post`,
    `${baseURL.robot}/admin/school`,
    {
      data
    }
  );
};

export interface LogisticsInfoByOrderIdReqQuery {
  /**
   * 订单ID
   */
  orderId: string;
}

export interface LogisticsInfoByOrderIdResBody {
  list: {
    /**
     * 发货单 ID
     */
    id: string;
    /**
     * 发货来源
     */
    deliverySource: string;
    /**
     * 发货来源单号
     */
    deliverySourceNo: string;
    /**
     * 洋葱 ID
     */
    onionId: string;
    /**
     * 发货单携带 SKU 编码信息
     */
    skuCodeList: string[];
    /**
     * 发货单状态
     */
    status: string;
    /**
     * 创建时间
     */
    createdAt: string;
    /**
     * 子发货单列表信息
     */
    subList: {
      /**
       * 子发货单 ID
       */
      id: string;
      /**
       * 子发货单状态
       */
      status: string;
      /**
       * 仓库
       */
      warehouseName: string;
      /**
       * 预计推送时间
       */
      expectedPushTime: string;
      /**
       * 出库单单号
       */
      documentCode: string;
      /**
       * 快递公司名称
       */
      expressCompany: string;
      /**
       * 快递公司编码
       */
      trackingNum: string;
      /**
       * 子发货单发货 SKU 名称
       */
      skuNameList: string[];
      /**
       * 发货标签
       */
      deliveryTags: string;
      /**
       * 旧-子发货状态
       */
      deliveryStatus: string;
      /**
       * 子发货单发货 SKU 编码
       */
      skuCodeList: string[];
    }[];
    /**
     * 旧-发货单状态
     */
    deliveryStatus: string;
  }[];
}

/**
 * @description 根据订单id获取物流信息
 * https://yapi.yc345.tv/project/2352/interface/api/126063
 * <AUTHOR>
 * @date 2025-05-15
 * @export
 * @param {LogisticsInfoByOrderIdReqQuery} params
 * @returns {Promise<LogisticsInfoByOrderIdResBody>}
 */
export const getLogisticsInfoApi = (params: LogisticsInfoByOrderIdReqQuery) => {
  return http.request<LogisticsInfoByOrderIdResBody>(
    `get`,
    `${baseURL.api}/wuhan-datapool/info/getLogisticsInfoByOrderId`,
    {
      params
    }
  );
};
