/*
 * @Date         : 2024-04-01 17:12:03
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { Directive, DirectiveBinding } from "vue";
import { useUserStore } from "@/store/modules/user";

export const auth: Directive = {
  mounted(el: Element, binding: DirectiveBinding<any>) {
    const { value } = binding;
    const { authorizationMap } = useUserStore();
    if (!authorizationMap.includes(value)) {
      el.parentNode?.removeChild(el);
    }
  }
};
