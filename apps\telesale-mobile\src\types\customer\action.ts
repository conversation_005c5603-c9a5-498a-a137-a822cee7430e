/*
 * @Date         : 2024-03-26 14:07:35
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

export interface GiveForm {
  type: string;
  time?: number;
  targetid?: string;
  userid?: string;
  start?: number;
  end?: number;
  targetIdList: string[];
}

export interface PursuitForm {
  note?: string;
  intention?: string;
  infoUuid?: string;
  notifyTime?: string | number;
}

export interface CueForm {
  note: string;
  workerId?: number;
  infoUuids: string[];
}

export interface ExpireForm {
  infoUuid: string;
  expire?: number;
  isEndService: boolean;
}
