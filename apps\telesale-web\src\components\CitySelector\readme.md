# 地区选择组件

## 介绍
用于选择地区
## 引入

```js
import CitySelector from '/@/components/CitySelector/index';
```

## 基础用法

```tsx
<teplate>
  <CitySelector
    ref="citySelectorRef"
    v-model:value="searchForm.regionInfos"
    :options="{ provinceKey: 'provinceCode', cityKey: 'cityCodes' }"
  />
</teplate>

<script lang="ts" setup>
  import { ref } from 'vue';
  import CitySelector from '/@/components/CitySelector/index';

  const searchForm = ref({
    regionInfos: []
  });
</script>
```

## Api

### Props

| 参数            | 说明                                                               | 类型      | 默认值  |
| --------------- | ------------------------------------------------------------------ | --------- | ------- |
| v-model:value   | 双向邦定的value值                                                    | CityValue[] |     |
| options         |  配置选项, 请参阅下面 optionsProps 表。   | Object |   { provinceKey: "province", cityKey: "city"}  |


### optionsProps
| 参数            | 说明                                                               | 类型      | 默认值  |
| --------------- | ------------------------------------------------------------------ | --------- | ------- |
| provinceKey   | 返回的省份字段key                                                    | string |  'province'   |
| cityKey   | 返回的城市字段key                                                    | string |  'city'   |
