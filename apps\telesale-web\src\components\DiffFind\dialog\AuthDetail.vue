<script lang="ts" setup>
import { computed } from "vue";
import { useAppStoreHook } from "/@/store/modules/app";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import { TableColumns } from "../../ReTable/types";

interface Props {
  value: boolean;
  dataMemery: any;
}
interface Emits {
  (e: "update:value", val: boolean): void;
}

const emit = defineEmits<Emits>();
const props = defineProps<Props>();
const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

function handleClose() {
  isModel.value = false;
}

let device = useAppStoreHook().device;

const listHeader = [
  { field: "name", desc: "订单名称" },
  { field: "id", desc: "订单ID" },
  { field: "amountName", desc: "实际抵扣金额" }
];
const listHeaderAuth: TableColumns[] = [
  { field: "name", desc: "权益" },
  // { field: "powerName", desc: "权益来源" },
  // { field: "fromId", desc: "来源ID" },
  // { field: "deductiblePriceName", desc: "可抵扣金额" },
  {
    field: "amount",
    desc: "实际抵扣金额",
    customRender: ({ text }) => {
      return "¥" + text.toFixed(2);
    }
  },
  {
    field: "days",
    desc: "实际抵扣时长",
    customRender: ({ text }) => {
      return Math.ceil(text) + "天";
    }
  }
];
const objectSpanMethod = ({ rowIndex, columnIndex }) => {
  let span = props.dataMemery.deductibleAuthDetailList[rowIndex].span;
  if (columnIndex === 0 || columnIndex === 5) {
    return {
      rowspan: span,
      colspan: span ? 1 : 0
    };
  }
};
</script>

<template>
  <el-dialog
    title="用户权益详情"
    v-model="isModel"
    :before-close="handleClose"
    append-to-body
    draggable
    close-on-click-modal
    width="80%"
  >
    <template v-if="dataMemery.deductibleOrders?.length">
      <div class="d-auth-title">7天内订单信息</div>
      <div class="g-pad-b-20">
        <ReTable
          v-if="device !== 'mobile'"
          ref="tableRefs"
          :dataList="dataMemery.deductibleOrders"
          :listHeader="listHeader"
        />
        <template v-else>
          <ReCardList
            ref="cardRefs"
            :dataList="dataMemery.deductibleOrders"
            :listHeader="listHeader"
            :isCardBox="false"
          />
        </template>
      </div>
    </template>
    <template v-if="dataMemery.deductibleAuthDetailList?.length">
      <div class="d-auth-title">权益信息</div>
      <div class="g-pad-b-20">
        <ReTable
          v-if="device !== 'mobile'"
          ref="tableRefs"
          :dataList="dataMemery.deductibleAuthDetailList"
          :listHeader="listHeaderAuth"
        />
        <template v-else>
          <ReCardList
            ref="cardRefs"
            :dataList="dataMemery.deductibleAuthDetailList"
            :listHeader="listHeaderAuth"
            :isCardBox="false"
          />
        </template>
      </div>
    </template>
  </el-dialog>
</template>
<style scoped lang="scss">
.d-auth-title {
  font-weight: bold;
  margin-bottom: 10px;
}
</style>
