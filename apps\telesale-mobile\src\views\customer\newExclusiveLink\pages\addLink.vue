<script lang="ts" setup>
import { ref } from "vue";
import { createH5LinkApi, XinXiLinkReq } from "@/api/customer/exclusiveLink";
import { closeToast, showLoadingToast, showToast } from "vant";
import { useRoute, useRouter } from "vue-router";
import MySelect from "@/components/MySelect/index.vue";
import {
  useQrcode,
  getNewDuration,
  newSchoolYearList,
  getStageDuration,
  getLabel,
  getStageGood,
  ipadNoneMap
} from "@telesale/shared";
import { getAuth } from "@/utils/common/auth";
import SaveImageDialog from "@/components/SaveImageDialog/index.vue";
import { cloneDeep } from "lodash-es";
import { GoodsConfig } from "@telesale/shared/src/businessHooks/payPush/types";
import { getGoodConfigApi } from "@/api/customer/details";
import { useUserStore } from "@/store/modules/user";
import { storeToRefs } from "pinia";
import {
  isStagesList,
  stagesType
} from "@telesale/shared/src/data/exclusiveLink";
import { getInstallmentPayType } from "@telesale/shared/src/businessHooks/payPush/installmentPay";
import { getArrayBufferBase64 } from "@/utils/common";

const router = useRouter();

// const { qrcodeList, qrcodeType, qrcodeValue } = useQrcode(getAuth);

const form = ref<XinXiLinkReq>({
  schoolYear: "",
  duration: undefined,
  from: "telesale",
  exchange: "",
  goodType: "common",
  stage: "",
  url: "",
  courseName: "",
  goods: undefined,
  isInstallment: 2,
  installmentPayType: ["alipayFq"]
});

const imgUrl = ref<string>("");
const show = ref<boolean>(false);
const goodList = ref<GoodsConfig[]>([]);
const goodsMobile = ref<GoodsConfig>();
const { isStages } = storeToRefs(useUserStore());
const ipadTypeList = ref<any[]>([]);

const changeInstallment = () => {
  form.value.installmentPayType = ["alipayFq"];
};

const changePad = e => {
  form.value.goods!.url = ipadTypeList.value.find(
    item => item.value === e
  )?.url;
};

function changeStage() {
  form.value.exchange = "";
  form.value.duration = undefined;
  form.value.goods = undefined;
  form.value.schoolYear = "";
  goodsMobile.value = undefined;
}

const myChangeGood = e => {
  e?.addPads?.forEach(item => {
    item.value = item.label;
  });
  ipadNoneMap[0].url = e?.url;
  ipadNoneMap[0].amount = e?.price || "";
  ipadTypeList.value = e?.addPads
    ? [...ipadNoneMap, ...e.addPads]
    : ipadNoneMap;

  console.log(ipadTypeList.value, ipadNoneMap);

  form.value.goods = e || undefined;
  form.value.exchange = "";
};

const getGoodsConfig = () => {
  showLoadingToast({});
  getGoodConfigApi()
    .then(res => {
      res.data.data.stage.forEach(item => {
        item.goodList.forEach(good => {
          if (item.cname === "high") {
            good.name += `-${good.price}元`;
          } else {
            good.name += `-${good.year}年`;
          }
        });
      });
      goodList.value = res.data.data.stage;
      form.value.stage = goodList.value?.[0]?.cname || undefined;
    })
    .finally(() => {
      closeToast();
    });
};
getGoodsConfig();

const onSubmit = () => {
  showLoadingToast({});
  // qrcodeType.value = form.value.from;
  const params = cloneDeep(form.value);
  params.exchange = params.exchange || "";
  const { param = undefined } = params.goods!;
  params.stage = param?.stage ? param?.stage + "" : "";
  params.goodType = param?.goodType || "";
  params.schoolYear = param?.schoolYear || "";
  params.duration = param?.duration;
  params.url = params.goods?.url || "";
  params.dynamic = true;
  params.courseName = params.goods?.name || "";
  params.exchange = params.exchange || "";
  params.installmentPayType = getInstallmentPayType(
    form.value.isInstallment,
    form.value.installmentPayType as string[]
  );
  Reflect.deleteProperty(params, "isInstallment");
  Reflect.deleteProperty(params, "goods");
  showLoadingToast({});
  createH5LinkApi(params).then(async data => {
    const url = await getArrayBufferBase64(data);
    imgUrl.value = url;
    showToast("操作成功");
    show.value = true;
  });
};
</script>

<template>
  <div>
    <van-form required class="mt-20px" @submit="onSubmit">
      <van-cell-group inset>
        <!-- <MySelect
          v-model:value="form.from"
          label="项目组"
          placeholder="请选择项目组"
          :columns="qrcodeList"
          :rules="[
            { required: true, message: '请选择项目组', trigger: 'onChange' }
          ]"
        /> -->
        <van-field
          label="学段"
          name="stage"
          required
          :rules="[
            {
              required: true,
              message: '请选择学段',
              trigger: 'onChange'
            }
          ]"
        >
          <template #input>
            <van-radio-group
              v-model="form.stage"
              direction="horizontal"
              @change="changeStage"
            >
              <van-radio
                icon-size="12"
                v-for="item in goodList"
                :key="item.cname"
                :name="item.cname"
              >
                {{ item.name }}
              </van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <MySelect
          v-model:value="goodsMobile"
          name="picker"
          label="商品"
          placeholder="请选择商品"
          :columns="getStageGood(goodList, form.stage)"
          :options="{
            label: 'name',
            value: 'name'
          }"
          required
          :rules="[
            { required: true, message: '请选择商品', trigger: 'onChange' }
          ]"
          @confim="myChangeGood"
        />
        <van-field
          v-if="form.goods"
          name="exchange"
          label="平板"
          required
          :rules="[
            {
              required: true,
              message: '请选择平板',
              trigger: 'onChange'
            }
          ]"
        >
          <template #input>
            <van-radio-group
              v-model="form.exchange"
              direction="horizontal"
              @change="changePad"
            >
              <van-radio
                icon-size="12"
                v-for="item in ipadTypeList"
                :key="item.value"
                :name="item.value"
              >
                {{ item.label }}
              </van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field label="商品售价" v-if="form.exchange">
          <template #input>
            {{ getLabel(form.exchange, ipadTypeList, "amount", "value") }}
          </template>
        </van-field>
        <template v-if="isStages">
          <van-field
            name="radio"
            label="分期支付"
            required
            :rules="[
              {
                required: true,
                message: '请选择分期支付',
                trigger: 'onChange'
              }
            ]"
          >
            <template #input>
              <van-radio-group
                v-model="form.isInstallment"
                direction="horizontal"
                @change="changeInstallment"
              >
                <van-radio
                  v-for="(item, index) in isStagesList"
                  :key="index"
                  :name="item.value"
                >
                  {{ item.label }}
                </van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <van-field
            v-if="form.isInstallment === 1"
            name="checkboxGroup"
            label="分期支付方式"
            required
            :rules="[
              {
                required: true,
                message: '请选择分期支付方式',
                trigger: 'onChange'
              }
            ]"
          >
            <template #input>
              <van-checkbox-group
                v-model="form.installmentPayType"
                direction="horizontal"
              >
                <van-checkbox
                  v-for="(item, index) in stagesType"
                  :key="index"
                  :name="item.value"
                  shape="square"
                >
                  {{ item.label }}
                </van-checkbox>
              </van-checkbox-group>
            </template>
          </van-field>
        </template>
      </van-cell-group>
      <div style="margin: 16px" class="flex gap-20px">
        <van-button class="flex-1" type="primary" native-type="submit">
          生成二维码
        </van-button>
        <van-button class="flex-1" @click="router.back()">返回</van-button>
      </div>
    </van-form>
    <SaveImageDialog v-model:show="show" :imgUrl="imgUrl" />
  </div>
</template>

<style lang="scss" scoped></style>
