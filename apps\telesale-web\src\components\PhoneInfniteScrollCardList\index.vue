<script setup lang="ts">
import { ref, computed, watch } from "vue";

interface Props {
  loading: boolean;
  list: any[];
}
const props = withDefaults(defineProps<Props>(), {});
const dataList = ref([]);

let total = ref(0);
const scrollList = ref([]);
const noMore = computed(() => scrollList.value.length >= total.value);
const load = () => {
  scrollList.value.push.apply(scrollList.value, dataList.value.splice(0, 10));
};

const scrollBoxRef = ref(null);
function initDateHandle(data) {
  scrollBoxRef.value?.scrollIntoView();
  total.value = data.length;
  dataList.value = data;
  scrollList.value = [];
  if (total.value) {
    scrollList.value.push.apply(
      scrollList.value,
      total.value < 10 ? dataList.value : dataList.value.splice(0, 10)
    );
  }
}

function clearDataHandel() {
  scrollBoxRef.value?.scrollIntoView();
  total.value = 0;
  dataList.value = [];
  scrollList.value = [];
}

watch(
  () => props.list,
  n => {
    console.log(n);
    if (n.length) {
      initDateHandle(n);
    } else {
      clearDataHandel();
    }
  },
  {
    deep: false,
    immediate: true
  }
);
</script>

<template>
  <div
    ref="scrollBoxRef"
    v-infinite-scroll="load"
    :infinite-scroll-disabled="loading || noMore"
  >
    <slot name="defalut" :dataList="scrollList" />
    <p v-if="noMore && total > 0" style="text-align: center">--没有更多了--</p>
  </div>
</template>
