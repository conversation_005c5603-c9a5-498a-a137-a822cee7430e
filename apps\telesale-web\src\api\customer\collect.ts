/*
 * @Date         : 2024-10-09 10:56:35
 * @Description  : 收藏相关api
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */
import { http } from "../../utils/http";
import baseURL from "../url";

export interface CollectInfo {
  id?: number;
  bookmarkName: string;
}

/**
 * @description: 新增收藏夹
 * @param {CollectInfo} data
 */
export const addCollectApi = (data: CollectInfo) => {
  return http.request("post", `${baseURL.api}/wuhan-datapool/bookmark/create`, {
    data
  });
};

/**
 * @description: 更新收藏夹
 * @param {CollectInfo} data
 */
export const updateCollectApi = (data: CollectInfo) => {
  return http.request("post", `${baseURL.api}/wuhan-datapool/bookmark/update`, {
    data
  });
};

/**
 * @description: 删除收藏夹
 * @param {number} id
 */
export const deleteCollectApi = (params: { id: number }) => {
  return http.request("get", `${baseURL.api}/wuhan-datapool/bookmark/delete`, {
    params
  });
};

export interface CollectList extends CollectInfo {
  showHandle?: boolean;
  child: CollectInfo[];
}

/**
 * @description: 获取收藏夹列表
 * @param {number} workerId
 */
export const getCollectApi = (params: { workerId: number; id?: number }) => {
  return http.request<{
    bookmarkInfos: CollectList[];
  }>("get", `${baseURL.api}/wuhan-datapool/bookmark/get`, { params });
};

/**
 * @description: 添加线索到收藏夹
 * @param {number} id
 * @param {string} infoUuid
 */
export const addClueCollectApi = (data: { infoUuid: string; id: number }) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/bookmark/collect/add`,
    { data }
  );
};

/**
 * @description: 移除收藏线索
 * @param {string} infoUuid
 */
export const delClueCollectApi = (data: { infoUuid: string[] }) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-datapool/bookmark/collect/delete`,
    { data }
  );
};

/**
 * @description: 获取收藏夹列表
 * @param {string} infoUuids
 */
export const getClueCollectApi = (data: { infoUuids: string[] }) => {
  return http.request<{
    collectInfos: { id: number; infoUuid: string }[];
  }>("post", `${baseURL.api}/wuhan-datapool/bookmark/collect/list`, { data });
};

interface CollectClueInfo {
  infoUuid: string;
  onionId: string;
  phone: string;
  description: string;
  historyAmount: number;
  lastActiveTime: number;
  callState: number;
  callCount: number;
  lastDial: number;
  source: string;
  role: string;
  stage: string;
  grade: string;
  createdAt: string;
  userExpire: string;
  workerId: number;
}

/**
 * @description: 获取收藏的线索列表
 * @param {string} infoUuids
 */
export const getClueCollectListApi = data => {
  return http.request<{
    list: CollectClueInfo[];
    infos: CollectClueInfo[];
    total: number;
  }>("post", `${baseURL.api}/wuhan-datapool/bookmark/collect/info/get`, {
    data
  });
};
