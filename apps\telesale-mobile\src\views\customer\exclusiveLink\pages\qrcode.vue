<script lang="ts" setup>
import { ref } from "vue";
import {
  LinkReq,
  getH5LinkApi,
  getLinkInfoApi
} from "@/api/customer/exclusiveLink";
import { closeToast, showLoadingToast, showToast } from "vant";
import { useRoute, useRouter } from "vue-router";
import MySelect from "@/components/MySelect/index.vue";
import { useQrcode } from "@telesale/shared";
import { getAuth } from "@/utils/common/auth";
import SaveImageDialog from "@/components/SaveImageDialog/index.vue";
import { useUserStore } from "@/store/modules/user";
import { storeToRefs } from "pinia";
import {
  isStagesList,
  stagesType
} from "@telesale/shared/src/data/exclusiveLink";
import { getInstallmentPayType } from "@telesale/shared/src/businessHooks/payPush/installmentPay";
import { getArrayBufferBase64 } from "@/utils/common";

const { query } = useRoute();

const router = useRouter();

// const { qrcodeList, qrcodeType, qrcodeValue } = useQrcode(getAuth);

const form = ref<LinkReq>({
  from: "telesale",
  id: "",
  dynamic: true,
  isInstallment: 2,
  installmentPayType: ["alipayFq"],
  courseName: ""
});

const imgUrl = ref<string>("");
const show = ref<boolean>(false);
const { isStages } = storeToRefs(useUserStore());

const changeInstallment = () => {
  form.value.installmentPayType = ["alipayFq"];
};

const getInfo = () => {
  showLoadingToast({});
  getLinkInfoApi({ id: parseInt(query.id as string) })
    .then(data => {
      form.value.id = data.courseId;
      form.value.courseName = data.name;
    })
    .finally(() => {
      closeToast();
    });
};

query.id && getInfo();

const onSubmit = () => {
  showLoadingToast({});
  // qrcodeType.value = form.value.from;
  const params = {
    ...form.value
  };
  params.installmentPayType = getInstallmentPayType(
    form.value.isInstallment,
    form.value.installmentPayType as string[]
  );
  Reflect.deleteProperty(params, "isInstallment");
  getH5LinkApi(params).then(async data => {
    const url = await getArrayBufferBase64(data);
    imgUrl.value = url;
    showToast("操作成功");
    show.value = true;
  });
};
</script>

<template>
  <div>
    <van-form required class="mt-20px" @submit="onSubmit">
      <van-cell-group inset>
        <van-field readonly label="商品名称">
          <template #input>
            {{ form.courseName }}
          </template>
        </van-field>
        <!-- <MySelect
          v-model:value="form.from"
          label="项目组"
          placeholder="请选择项目组"
          :columns="qrcodeList"
          :rules="[
            { required: true, message: '请选择项目组', trigger: 'onChange' }
          ]"
        /> -->
        <template v-if="isStages">
          <van-field
            name="radio"
            label="分期支付"
            required
            :rules="[
              {
                required: true,
                message: '请选择分期支付',
                trigger: 'onChange'
              }
            ]"
          >
            <template #input>
              <van-radio-group
                v-model="form.isInstallment"
                @change="changeInstallment"
                direction="horizontal"
              >
                <van-radio
                  v-for="(item, index) in isStagesList"
                  :key="index"
                  :name="item.value"
                >
                  {{ item.label }}
                </van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <van-field
            v-if="form.isInstallment === 1"
            name="checkboxGroup"
            label="分期支付方式"
            required
            :rules="[
              {
                required: true,
                message: '请选择分期支付方式',
                trigger: 'onChange'
              }
            ]"
          >
            <template #input>
              <van-checkbox-group
                v-model="form.installmentPayType"
                direction="horizontal"
              >
                <van-checkbox
                  v-for="(item, index) in stagesType"
                  :key="index"
                  :name="item.value"
                  shape="square"
                >
                  {{ item.label }}
                </van-checkbox>
              </van-checkbox-group>
            </template>
          </van-field>
        </template>
      </van-cell-group>
      <div style="margin: 16px" class="flex gap-20px">
        <van-button class="flex-1" type="primary" native-type="submit">
          生成二维码
        </van-button>
        <van-button class="flex-1" @click="router.back()">返回</van-button>
      </div>
    </van-form>
    <SaveImageDialog v-model:show="show" :imgUrl="imgUrl" />
  </div>
</template>

<style lang="scss" scoped></style>
