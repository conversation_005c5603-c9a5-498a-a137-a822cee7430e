import { http } from "../utils/http";
import baseURL from "./url";

// 渠道列表
export const getChannelListApi = () => {
  return http.request(
    "get",
    `${baseURL.api}/customer-service/link/channel/list`
  );
};

// 渠道详情
export const getChannelInfoApi = params => {
  return http.request("get", `${baseURL.api}/customer-service/link/channel`, {
    params
  });
};

// 新增渠道
export const addChannelApi = data => {
  return http.request("post", `${baseURL.api}/customer-service/link/channel`, {
    data
  });
};

// 更新渠道
export const updateChannelApi = data => {
  return http.request("put", `${baseURL.api}/customer-service/link/channel`, {
    data
  });
};

// 场景列表
export const getSceneListApi = params => {
  return http.request("get", `${baseURL.api}/customer-service/link/list`, {
    params
  });
};

// 场景详情
export const getScenelInfoApi = params => {
  return http.request("get", `${baseURL.api}/customer-service/link`, {
    params
  });
};

// 新增场景
export const addScenelApi = data => {
  return http.request("post", `${baseURL.api}/customer-service/link`, {
    data
  });
};

// 更新场景
export const updateScenelApi = data => {
  return http.request("put", `${baseURL.api}/customer-service/link`, {
    data
  });
};

// 评论统计
export const getMaterialApi = () => {
  return http.request(
    "get",
    `${baseURL.api}/customer-service/material/appraise/statistics`
  );
};

// 获取h5设置
export const getH5SettingApi = () => {
  return http.request(
    "get",
    `${baseURL.api}/customer-service/link/h5/settings`
  );
};

// 保存h5设置
export const updateH5SettingApi = data => {
  return http.request(
    "post",
    `${baseURL.api}/customer-service/link/h5/settings`,
    { data }
  );
};
