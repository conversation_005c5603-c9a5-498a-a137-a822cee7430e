# 地区选择组件

## 介绍
用户筛选模块的展开和收起
## 引入

```js
import FormSearch from '/@/components/FormSearch/index';
```

## 基础用法

```tsx
<teplate>
   <FormSearch @onSearch="onSearch" @onReset="resetForm()">
      <template #show>
        <el-form-item prop="onionid">
          <el-input
            v-model="searchForm.onionId"
            placeholder="请输入洋葱ID"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
      </template>
      <template #hide>
        <el-form-item prop="isLock">
          <el-select
            v-model="searchForm.isLock"
            placeholder="请选择锁定用户"
            clearable
          >
            <el-option label="全部" :value="false" />
            <el-option label="锁定用户" :value="true" />
          </el-select>
        </el-form-item>
      </template>
    </FormSearch>
</teplate>

<script lang="ts" setup>
  import { ref } from 'vue';
  import CitySelector from '/@/components/CitySelector/index';

  const searchForm = ref({
    onionId: undefined,
    isLock: false,
  });

  const onSearch = () => {
    // 数据筛选逻辑
  }

  const resetForm = () => {
    // 筛选重置逻辑
  }
</script>
```

## Api

### Props

| 参数            | 说明                                                               | 类型      | 默认值  |
| --------------- | ------------------------------------------------------------------ | --------- | ------- |
| type  | 按钮是重置筛选还是清除筛选                                                | enum |   'reset'  |

### Events
| 事件名            | 说明                                                               | 类型      | 
| --------------- | ------------------------------------------------------------------ | --------- |
|     on-search     | 点击筛选按钮触发                                                        | Function | 
|     on-reset      | 按钮是重置/清除筛选触发                                                    | Function | 

### Slots
| 插槽名            | 说明                                                               | 
| --------------- | ------------------------------------------------------------------ | 
| show  |    默认展示的筛选项                                             |
| hide  |    默认隐藏的筛选项                                             |
| btns  |    自定义功能按钮                                             |