/*
 * @Date         : 2023-08-10 14:01:07
 * @Description  : datepicker配置
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { dayjs } from 'element-plus'
import { ref, computed, watch } from 'vue'

export default function(_value) {
  const startTime = ref()
  const endTime = ref()

  const defaultTime: [Date, Date] = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)] // 当日具体时刻 00:00:00 - 23:59:59
  const defaultValue = dayjs().subtract(1, 'month').toDate()  // 选择器打开默认显示时间 计算提前一个月 使当前月在右边显示

  function calendarChange(val: Date[]) { // 更改日期事件
    startTime.value = val[0]
    endTime.value = val[1]
  }

  watch(_value, newV => { // 清空日期 重置起始结束时间
    if (!newV) {
      startTime.value = null
      endTime.value = null
    }
  })

  function initDatePickToDay() {
    _value.value = [dayjs().hour(0).minute(0).second(0), dayjs().hour(23).minute(59).second(59)]
  }

  function initDatePickToMonth() {
    _value.value = [dayjs().hour(0).minute(0).second(0).subtract(1, 'month'), dayjs().hour(23).minute(59).second(59)]
  }

  function initDatePickToCurrentMonth() {
    _value.value = [dayjs().startOf('month'), dayjs().hour(23).minute(59).second(59)]
  }

  const unixsDatePicker = computed(() => {
    if (!_value.value) return [null, null]
    return _value.value.map(item => {
      return dayjs(item).unix()
    })
  })

  const unixmsDatePicker = computed(() => {
    if (!_value.value) return [null, null]
    return _value.value.map(item => {
      return dayjs(item).valueOf()
    })
  })

  return { calendarChange, defaultTime, defaultValue, initDatePickToDay, initDatePickToMonth, initDatePickToCurrentMonth, unixmsDatePicker, unixsDatePicker, startTime }
}
