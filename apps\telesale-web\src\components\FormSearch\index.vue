<!--
 * @Date         : 2024-07-19 11:42:44
 * @Description  : 列表筛选组件
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="tsx" setup>
import { ref } from "vue";
import { useRenderIcon } from "../ReIcon/src/hooks";
import { ArrowUp, ArrowDown } from "@element-plus/icons-vue";

const props = withDefaults(
  defineProps<{
    type?: "clear" | "reset";
    timeSortData?: any[];
  }>(),
  {
    type: "reset"
  }
);

const emits = defineEmits(["onSearch", "onReset", "sortSearch"]);

const collapsed = ref<boolean>(true);

const changeMore = () => {
  collapsed.value = !collapsed.value;
};

const onSearch = () => {
  emits("onSearch");
};

const onReset = () => {
  emits("onReset");
};
</script>

<template>
  <div>
    <slot name="show" />
    <span v-show="!collapsed">
      <slot name="hide" />
    </span>

    <el-form-item>
      <el-button
        type="primary"
        :icon="useRenderIcon('search')"
        @click="onSearch"
      >
        搜索
      </el-button>
      <el-button :icon="useRenderIcon('refresh')" @click="onReset">
        {{ props.type === "reset" ? "重置" : "清空筛选条件" }}
      </el-button>
      <slot name="btns" />

      <el-button type="primary" link @click="changeMore">
        {{ collapsed ? "更多筛选" : "收起" }}
        <el-icon class="el-icon--right">
          <component :is="collapsed ? ArrowDown : ArrowUp" />
        </el-icon>
      </el-button>
    </el-form-item>
  </div>
</template>

<style lang="scss" scoped></style>
