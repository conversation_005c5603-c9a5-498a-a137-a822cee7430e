/*
 * @Date         : 2024-03-27 12:05:35
 * @Description  : 转介绍相关api
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */

import { http } from "../../utils/http";
import baseURL from "../url";

export interface DeliveryParams {
  mobile: string;
  rewardName: string;
  deliverGoodsStatus: number;
  rewardAtStart: number;
  rewardAtEnd: number;
  rewardTime: any[];
}

interface DeliveryBody {
  rewardId?: number;
  rewardName?: string;
  rewardAt?: string;
  deliverGoodsStatus?: number;
  logisticsId?: string;
  trackingNum?: string;
  address?: string;
  expressName?: string;
  consignee?: string;
  receiverMobile?: string;
  mobile?: string;
}

/**
 * @description: 获取中奖列表
 * @param {DeliveryParams} data
 * @return {DeliveryBody}
 */

export const getDeliveyListApi = (data: DeliveryParams) => {
  return http.request<{
    list: DeliveryBody[];
    total: number;
  }>(
    "post",
    `${baseURL.api}/wuhan-miniprogram/wechat_fission/listRewardsRecord`,
    {
      data
    }
  );
};

/**
 * @description: 上传订单
 */
export const uploadOrderApi = data => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-miniprogram/wechat_fission/parseRewardsExcel`,
    { data },
    {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

/**
 * @description: 导出订单
 */
export const exportOrderApi = (data: DeliveryParams) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-miniprogram/wechat_fission/exportRewardsRecode`,
    {
      data
    },
    {
      responseType: "blob"
    }
  );
};

interface DeliveryInfo {
  logisticsId: string;
  trackingNum: string;
  expressName: string;
}

/**
 * @description: 设置物流信息
 */
export const setDeliveryInfoApi = (data: DeliveryInfo) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-miniprogram/wechat_fission/updateRewardsRecord`,
    {
      data
    }
  );
};

/**
 * @description: 批量更新奖品记录
 */
export const setBatchDeliveryInfoApi = (data: { records: DeliveryInfo }) => {
  return http.request(
    "post",
    `${baseURL.api}/wuhan-miniprogram/wechat_fission/batchUpdateRewardsRecord`,
    {
      data
    }
  );
};
