<!--
  * @Date         : 2025-01-14 18:24:57
  * @Description  :
  * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { ref } from "vue";
import StudyReport from "./StudyReport.vue";
import StudyAnalze from "./StudyAnalze.vue";
import Stage<PERSON><PERSON><PERSON> from "./StageAnalze.vue";
import { getIframeTokenApi } from "@/api/customer/details";

const props = defineProps<{
  userId: string;
}>();
const active = ref();
const token = ref("");

function init() {
  getIframeTokenApi({
    userId: props.userId,
    source: "dianxiao"
  }).then(res => {
    token.value = res.token;
  });
}

props.userId && init();
</script>

<template>
  <div>
    <van-tabs v-model:active="active">
      <van-tab title="学情报告">
        <StudyReport :userId="props.userId" :token="token" />
      </van-tab>
      <van-tab title="学情分析">
        <StudyAnalze :userId="props.userId" :token="token" />
      </van-tab>
      <van-tab title="学科分析">
        <StageAnalze :userId="props.userId" />
      </van-tab>
    </van-tabs>
  </div>
</template>
