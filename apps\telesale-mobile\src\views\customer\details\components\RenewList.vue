<script lang="ts" setup>
const props = defineProps<{
  show: boolean;
  listHeader: any[];
  dataList: any[];
  title: string;
  getEndTime: Function;
  activePadList?: any[];
  repurchaseType: string;
}>();
const emits = defineEmits(["update:show", "goCode"]);
const showValue = computed({
  get() {
    return props.show;
  },
  set(val: boolean) {
    emits("update:show", val);
  }
});

const goCode = child => {
  emits("goCode", child);
};
</script>

<template>
  <div class="px-20px">
    <div class="text-28px mb-10px c-red ml-40px" v-if="getEndTime(dataList)">
      续购资格失效时间：{{ getEndTime(dataList) }}
    </div>

    <div class="group" v-if="dataList.length">
      <div class="group-item" v-for="child in dataList" :key="child.id">
        <div class="good-name" v-html="child.name" />
        <div class="flex flex-wrap">
          <template v-for="item in listHeader" :key="item.field">
            <div
              class="good-info"
              v-if="item.field !== 'name' && item.field !== 'paidGoodName'"
            >
              {{ item.desc }}
              {{
                `${
                  item.filters ? item.filters(child) : child[item.field] || "-"
                }`
              }}
            </div>
          </template>
        </div>
        <div>
          <div class="qrcode-btn" @click="goCode(child)">动态二维码</div>
        </div>
      </div>
    </div>
    <van-empty v-else description="暂无数据" />
  </div>
</template>

<style lang="scss" scoped>
.show-title {
  padding: 10px 20px;
  background-color: #fae9fa;
  color: #6c0abc;
  border-radius: 10px;
  margin: 20px 0 20px 10px;
}
.group {
  padding: 0 20px;
  border-radius: 20px;
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;
  &-item {
    padding: 20px;
    border-bottom: 1px solid #b4b4b4;
  }
  &-item:last-child {
    border-bottom: 0;
  }
  .good-name {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 20px;
  }
  .good-info {
    padding: 10px 20px;
    background-color: #fff9e9;
    color: #813300;
    font-size: 24px;
    border-radius: 10px;
    margin-right: 20px;
    margin-bottom: 10px;
  }
  .qrcode-btn {
    color: #3370ff;
    font-size: 24px;
    margin-top: 20px;
  }
}
</style>
